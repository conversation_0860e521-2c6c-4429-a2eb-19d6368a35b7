#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Complete encoding fix by replacing all Chinese characters with English
"""

import re
import os

def fix_encoding():
    file_path = "src/ahpu_control/ahpu_control/control_yolov5.py"
    
    # Read the file with UTF-8 encoding
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        # If UTF-8 fails, try with error handling
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()
    
    # Complete Chinese to English replacements
    replacements = {
        # Comments
        "# 没有轨迹线": "# No trajectory lines",
        "# 只有一个轨迹线，无法拟合，直接返回": "# Only one trajectory line, cannot fit, return directly",
        "# 有两个轨迹线，线性拟合": "# Two trajectory lines, linear fitting",
        "# 有两个轨迹线，返回最远的那个": "# Two trajectory lines, return the farthest one",
        "# 有多个轨迹线，曲线拟合": "# Multiple trajectory lines, curve fitting",
        "# 线程锁": "# Thread lock",
        "# 默认是停车状态": "# Default is parking state",
        "# 存储yolov5的检测结果": "# Store yolov5 detection results",
        "# 初始化targets": "# Initialize targets",
        "# 创建订阅者，订阅Yolov5的检测结果": "# Create subscriber for Yolov5 detection results",
        "# 创建发布者，发布控制指令": "# Create publisher for control commands",
        "# 创建订阅者，订阅小车的状态": "# Create subscriber for car state",
        "# 创建定时器": "# Create timer",
        "# 订阅yolov5的检测结果, 更新targets": "# Subscribe to yolov5 detection results, update targets",
        "# 停车重置": "# Parking reset",
        "# 执行任务1": "# Execute task 1",
        "# 执行任务2": "# Execute task 2", 
        "# 执行任务3": "# Execute task 3",
        "# 任务1策略代码 - 优化版：智能目标搜索": "# Task 1 strategy code - Optimized: intelligent target search",
        "# 任务3策略代码": "# Task 3 strategy code",
        "# 调试：打印处理后的数组长度": "# Debug: print processed array lengths",
        "# 准备检测信息（停车任务）": "# Prepare detection info (parking task)",
        "# 智能避障算法": "# Intelligent obstacle avoidance algorithm",
        "# 更新避障状态（传入检测信息）": "# Update avoidance state (pass detection info)",
        "# 没有障碍物，重置避障状态": "# No obstacles, reset avoidance state",
        "# 处理圆锥数据：[x_center, y_bottom, width]": "# Process cone data: [x_center, y_bottom, width]",
        "# 检测是否有障碍物阻挡路径": "# Check if obstacles block the path",
        "# 路径清晰，执行智能回正逻辑": "# Path clear, execute intelligent return logic",
        "# 检查是否有实时地标线信息": "# Check for real-time landmark line info",
        "# 选择最近的障碍物": "# Select nearest obstacle",
        "# 判断避障方向": "# Determine avoidance direction",
        "# 计算避障目标点": "# Calculate avoidance target point",
        "# 障碍物在左侧，向右避障": "# Obstacle on left, avoid right",
        "# 障碍物在右侧，向左避障": "# Obstacle on right, avoid left",
        "# 边界检查，确保目标点在合理范围内": "# Boundary check, ensure target in reasonable range",
        "# 更新避障状态": "# Update avoidance state",
        "# 智能回正策略：优先寻找实时地标线": "# Intelligent return strategy: prioritize real-time landmark lines",
        "# 还在避障中，继续当前策略": "# Still avoiding, continue current strategy",
        "# 执行智能回正策略": "# Execute intelligent return strategy",
        "# 策略1：如果能看到地标线，优先跟随实时地标线": "# Strategy 1: If landmark lines visible, prioritize real-time lines",
        "# 策略2：没有地标线，向图像中心区域搜索": "# Strategy 2: No landmark lines, search center area",
        "# 执行回正策略：基于原始目标位置进行方向性回正": "# Execute return strategy: directional return based on original target position",
        "# 关键：如果有原始目标点作为参考，进行方向性搜索": "# Key: If original target as reference, directional search",
        "# 根据原始位置调整搜索方向": "# Adjust search direction based on original position",
        "# 原来在左侧": "# Originally on left",
        "# 原来在右侧": "# Originally on right",
        "# 原来在中间": "# Originally in center",
        "# 回正超时处理": "# Return timeout handling",
        "# 约3秒回正时间": "# About 3 seconds return time",
        "# 返回中心位置": "# Return to center position",
        "# 检查是否有锥桶阻挡目标路径": "# Check if cones block target path",
        "# 绕过锥桶寻找二维码的策略": "# Strategy to navigate around cones to find QR codes",
        "# 初始化搜索状态": "# Initialize search state",
        "# 默认向右搜索": "# Default search right",
        "# 分析锥桶分布，选择绕行方向": "# Analyze cone distribution, choose detour direction",
        "# 找到最近的锥桶": "# Find nearest cone",
        "# 根据锥桶位置决定绕行方向": "# Decide detour direction based on cone position",
        "# 盲飞导航：当什么都检测不到时的应急策略": "# Blind navigation: emergency strategy when nothing detected",
        "# 初始化盲飞状态": "# Initialize blind navigation state",
        "# 盲飞策略选择": "# Blind navigation strategy selection",
        "# 策略1：向最后有效目标点前进": "# Strategy 1: move to last valid target point",
        "# 策略2：向原始目标点回归": "# Strategy 2: return to original target point",
        "# 策略3：保持直行": "# Strategy 3: keep going straight",
        "# 紧急恢复策略": "# Emergency recovery strategy",
        "# 返回安全的默认目标点": "# Return safe default target point",
        "# 更新最后有效目标点": "# Update last valid target point",
        "# 获取避障状态信息，用于调试": "# Get avoidance status info for debugging",
        "# 计算角度（注意处理除零情况）": "# Calculate angle (handle division by zero)",
        "# 角度控制": "# Angle control",
        "# 速度控制": "# Speed control",
        "# 角度和速度的缩放": "# Angle and speed scaling",
        "#从targets中获取每个检测物体的坐标结果": "# Get coordinate results for each detected object from targets",
        "#将数据清除": "# Clear data",
        "# 创建执行器，这里使用多线程": "# Create executor, using multi-threading here",
        
        # Log messages
        "运动控制节点启动成功": "Motion control node started successfully",
        "当前是停车状态": "Currently in parking state",
        "检测目标数量": "Number of detected targets",
        "线路数量": "Number of lines",
        "圆锥数量": "Number of cones", 
        "停车区域数量": "Number of parking areas",
        "二维码数量": "Number of QR codes",
        "进入任务1": "Entering task 1",
        "进入任务2": "Entering task 2",
        "进入任务3": "Entering task 3",
        "进入模型图生文": "Entering model image-to-text",
        "停车重置": "Parking reset",
        "保完赛": "Race completed",
        "检测到二维码，前往二维码": "QR code detected, moving to QR code",
        "二维码距离很近，停车识别": "QR code very close, stop for recognition",
        "未找到二维码，前往地标线": "QR code not found, moving to landmark line",
        "过滤前地标线数量": "Number of landmark lines before filtering",
        "过滤后地标线数量": "Number of landmark lines after filtering",
        "所有地标线都被过滤掉了！使用原始地标线": "All landmark lines filtered out! Using original landmark lines",
        "拟合后目标点": "Target point after fitting",
        "地标线拟合失败": "Landmark line fitting failed",
        "使用备用目标点": "Using backup target point",
        "没有可用的地标线，跳过此帧": "No available landmark lines, skip this frame",
        "只检测到锥桶，无地标线和二维码": "Only cones detected, no landmark lines or QR codes",
        "避障中只见锥桶，执行回正策略": "Only cones visible during avoidance, execute return strategy",
        "正常状态绕过锥桶寻找二维码": "Normal state, navigate around cones to find QR code",
        "回正控制": "Return control",
        "未检测到任何目标，启动盲飞导航": "No targets detected, starting blind navigation",
        "实际数组长度": "Actual array lengths",
        "盲飞控制": "Blind navigation control",
        "检测到停车点，前往停车点": "Parking spot detected, moving to parking spot",
        "未找到停车点，前往地标线": "Parking spot not found, moving to landmark line",
        "未检测到停车点或地标线，调整姿态": "No parking spot or landmark line detected, adjusting posture",
        "已到达P点": "Reached point P",
        "智能避障": "Intelligent obstacle avoidance",
        "障碍物位置": "Obstacle position",
        "原目标": "Original target",
        "新目标": "New target",
        "发现实时地标线，直接跟随": "Real-time landmark line found, following directly",
        "搜索地标线，目标区域": "Searching for landmark lines, target area",
        "回正搜索超时，重置状态": "Return search timeout, resetting state",
        "基于原始目标": "Based on original target",
        "向左回正到": "Return left to",
        "向右回正到": "Return right to", 
        "向中心回正到": "Return center to",
        "无原始目标信息，默认向中心回正到": "No original target info, default return center to",
        "回正超时，重置避障状态": "Return timeout, reset avoidance state",
        "开始绕过锥桶寻找二维码": "Start navigating around cones to find QR code",
        "向右绕过锥桶寻找二维码": "Navigate right around cones to find QR code",
        "向前寻找二维码": "Search forward for QR code",
        "向左绕过锥桶寻找二维码": "Navigate left around cones to find QR code",
        "直行寻找二维码": "Go straight to find QR code",
        "二维码搜索超时，恢复正常模式": "QR code search timeout, return to normal mode",
        "启动盲飞导航": "Starting blind navigation",
        "盲飞超时，尝试恢复": "Blind navigation timeout, attempting recovery",
        "盲飞目标": "Blind navigation target",
        "最后有效点": "Last valid point",
        "原始目标点": "Original target point",
        "直行": "Go straight",
        "执行紧急恢复": "Executing emergency recovery",
        "紧急目标": "Emergency target",
        "目标点坐标": "Target point coordinates",
        "速度": "Speed",
        "角度": "Angle",
        
        # Variable names and other text
        "筛选x坐标大于300的线，尽量过滤左侧地标线": "Filter lines with x > 300, try to filter left landmark lines",
        "x+w/2, y+h: 二维码的下沿中点坐标": "x+w/2, y+h: QR code bottom center coordinates",
        "取y坐标最小的二维码, 即最远的二维码": "Take QR code with smallest y coordinate, i.e., farthest QR code",
        "二维码距离很近": "QR code very close",
        "x+w/2, y+h/2: 地标线中心点坐标": "x+w/2, y+h/2: landmark line center coordinates",
        "通过拟合曲线拟合得到目标点": "Get target point through curve fitting",
        "拟合结果无效": "Fitting result invalid",
        "x+w/2, y+h/2: 停车点的中点坐标": "x+w/2, y+h/2: parking spot center coordinates",
        "取y坐标最小的停车点, 即最远的停车点": "Take parking spot with smallest y coordinate, i.e., farthest parking spot",
        "停车点距离很近": "Parking spot very close",
        "取y坐标最小的线, 即最远的地标线": "Take line with smallest y coordinate, i.e., farthest landmark line",
        "最远的地标线": "Farthest landmark line",
        "向图像中心偏下的区域移动，这里通常是地标线的可能位置": "Move to lower center area of image, usually possible landmark line position",
        "图像中心偏下": "Lower center of image",
        "如果有原始目标点作为参考，进行方向性搜索": "If original target as reference, directional search",
        "向左搜索": "Search left",
        "向右搜索": "Search right",
        "搜索超时处理": "Search timeout handling",
        "约3秒搜索时间": "About 3 seconds search time",
        "cone_y 不需要使用": "cone_y not needed",
        "图像中心，稍微靠下": "Image center, slightly lower",
        "第": "No.",
        "次": "times",
        "锥桶在左侧，向右绕行": "Cone on left, detour right",
        "向右绕行": "Detour right",
        "向前寻找": "Search forward",
        "锥桶在右侧，向左绕行": "Cone on right, detour left", 
        "向左绕行": "Detour left",
        "没有锥桶信息，向前搜索": "No cone info, search forward",
    }
    
    # Apply replacements
    for chinese, english in replacements.items():
        content = content.replace(chinese, english)
    
    # Remove any remaining Chinese characters in comments and strings
    # Replace Chinese characters with placeholder
    content = re.sub(r'[\u4e00-\u9fff]+', '[Chinese text removed]', content)
    
    # Write back to file with UTF-8 encoding
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Complete encoding fix completed!")
    print("All Chinese characters have been replaced with English equivalents.")

if __name__ == "__main__":
    fix_encoding()
