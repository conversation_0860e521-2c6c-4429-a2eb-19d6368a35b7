import rclpy
from rclpy.logging import get_logger
from rclpy.node import Node

from sensor_msgs.msg import CompressedImage, Image
from std_msgs.msg import Float32MultiArray, MultiArrayDimension, Int32
from ahpu_interfaces.msg import Sign

import time
from hobot_dnn import pyeasy_dnn as dnn  # type: ignore
import numpy as np
import cv2
from cv_bridge import CvBridge


class MyNode(Node):
    def __init__(self, name):
        super().__init__(name)  # type: ignore

        self.logger = get_logger(self.get_name())

        # 创建发布者，发布小车状态
        self.pub_state = self.create_publisher(Int32, "/car_state", 1)

        # 创建订阅者，订阅foxglove信号
        self.sub_foxglove = self.create_subscription(
            Int32, "/sign_foxglove", self.sub_foxglove_callback, 1
        )
        
        # 创建发布者，向foxglove发布消息
        self.pub_sign_switch = self.create_publisher(Sign, "sign_switch", 1)

        # 创建订阅者，订阅发车信号
        self.sub_begin = self.create_subscription(
            Int32, "/begin_car", self.sub_begin_callback, 1
        )

        # 创建订阅者，订阅二维码识别情况
        self.sub_qr_code = self.create_subscription(
            Int32, "/qr_data", self.sub_qr_code_callback, 1
        )

        self.logger.info("小车状态控制节点启动成功")

    def pub_car_state(self, state):
        """
        重复发送三次，确保订阅者全部接收到
        0: 停车状态
        1: 任务1状态
        2: 任务2状态
        3: 任务3状态
        """

        msg = Int32()
        msg.data = state
        for i in range(3):
            self.pub_state.publish(msg)
            time.sleep(0.09)

    def sub_foxglove_callback(self, msg):
        if msg.data == 6:
            self.pub_car_state(3)  # 发布小车进入任务3状态
            self.logger.info("Foxglove消息: 完成任务2, 进入任务3状态")
        elif msg.data == 8:
            self.pub_car_state(8)
            self.logger.info("Foxglove消息:进入模型图生文阶段")

        # elif msg.data == 0:
        #   sign = Sign()
        #    sign.sign_data = 0
        #    self.pub_sign_switch.publish(sign)
        #    self.logger.info("Foxglove消息: 信号连接正常")
            

    def sub_begin_callback(self, msg):
        self.logger.info("接收到发车信号")
        if msg.data == 1:  # 接收到任务1信号
            self.pub_car_state(1)  # 发布小车进入任务1状态
            self.logger.info("进入任务1状态")
        elif msg.data == 2: # 接收到任务2信号
            self.pub_car_state(2)  # 发布小车进入任务1状态
            self.logger.info("进入任务2状态")
        elif msg.data == 3: # 接收到任务3信号
            self.pub_car_state(3)  # 发布小车进入任务1状态
            self.logger.info("进入任务3状态")
        elif msg.data == 0:  # 接收到停车信号
            self.pub_car_state(0)  # 发布小车为停车重置状态
            self.logger.info("停车重置")
        elif msg.data == 9: # 接收到任务9信号
            self.pub_car_state(9)  # 发布小车进入任务1状态
            self.logger.info("进入任务9状态")

    def sub_qr_code_callback(self, msg):
        self.pub_car_state(2)  # 发布小车进入任务2状态
        self.logger.info("进入任务2状态")

def main(args=None):
    rclpy.init(args=args)
    car_state = MyNode("car_state")
    try:
        rclpy.spin(car_state)
    except KeyboardInterrupt:
        pass
    car_state.destroy_node()
    rclpy.shutdown()
