import rclpy
import rclpy.logging
from rclpy.node import Node

from cmj_interfaces.msg import CenterPoint
from geometry_msgs.msg import Twist

import numpy as np


class Control(Node):
    def __init__(self, name):
        super().__init__(name)

        self.center_point = None
        self.scale_factor = 400 / 640
        self.d = 200

        rclpy.logging.get_logger("control").info(
            f"Inited. Scale factor: {self.scale_factor}"
        )

        # 创建订阅者，订阅赛道中点
        self.sub_center_point = self.create_subscription(
            CenterPoint, "/center_point", self.sub_center_point_callback, 10
        )

        # 创建发布者，发布控制指令
        self.pub_cmd_vel = self.create_publisher(Twist, "/cmd_vel", 10)

    # 赛道中点订阅者的回调函数
    def sub_center_point_callback(self, msg):
        rclpy.logging.get_logger("control").info(f"{msg}")
        # 获取中点x坐标
        x = msg.x
        
        # 创建控制消息
        twist_msg = Twist()

        twist_msg.linear.x = 0.2 # 设置前进的线速度
        twist_msg.linear.y = 0.0
        twist_msg.linear.z = 0.0

        twist_msg.angular.x = 0.0
        twist_msg.angular.y = 0.0
        twist_msg.angular.z = np.arctan(self.scale_factor * (320 - x) / self.d) * 0.75 # 计算转向的角速度

        rclpy.logging.get_logger("control").info(f"{twist_msg}")

        # 发布控制指令
        self.pub_cmd_vel.publish(twist_msg)

    # 在控制器退出后，将所有速度置为0，保证小车停止
    def finally_block(self):
        twist_msg = Twist()

        twist_msg.linear.x = 0.0
        twist_msg.linear.y = 0.0
        twist_msg.linear.z = 0.0

        twist_msg.angular.x = 0.0
        twist_msg.angular.y = 0.0
        twist_msg.angular.z = 0.0

        self.pub_cmd_vel.publish(twist_msg)


def main(args=None):
    rclpy.init(args=args)
    control = Control("cmj_control")
    try:
        rclpy.spin(control)
    finally:
        control.finally_block()
    control.destroy_node()
    rclpy.shutdown()
