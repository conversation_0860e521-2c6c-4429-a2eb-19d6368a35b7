#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能避障算法测试文件
测试避障逻辑的正确性和稳定性
"""

import sys
import os
import numpy as np
import unittest
from unittest.mock import Mock, patch

# 添加路径以导入控制模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'ahpu_control'))

class TestAvoidanceAlgorithm(unittest.TestCase):
    """避障算法测试类"""
    
    def setUp(self):
        """测试初始化"""
        # 模拟ROS2环境
        with patch('rclpy.node.Node'):
            from control_yolov5 import AdvancedAvoid, AvoidanceConfig
            self.avoid = AdvancedAvoid()
            self.config = AvoidanceConfig()
    
    def test_avoidance_config(self):
        """测试配置参数"""
        self.assertGreater(self.config.SAFETY_MARGIN, 1.0)
        self.assertGreater(self.config.AVOID_DISTANCE_FACTOR, 1.0)
        self.assertLess(self.config.RETURN_SMOOTHNESS, 1.0)
        self.assertGreater(self.config.MAX_AVOID_TIME, 0)
    
    def test_avoid_state_transitions(self):
        """测试避障状态转换"""
        # 初始状态
        self.assertEqual(self.avoid.avoid_state, "NORMAL")
        
        # 模拟避障开始
        self.avoid.avoid_state = "AVOIDING"
        self.avoid.avoid_start_time = 0.0
        
        # 测试超时转换
        self.avoid.update_avoid_state(self.config.MAX_AVOID_TIME + 1.0)
        self.assertEqual(self.avoid.avoid_state, "RETURNING")
    
    def test_obstacle_detection(self):
        """测试障碍物检测逻辑"""
        # 模拟目标点
        target = np.array([500, 300])
        
        # 模拟圆锥数据 [x, y, w, h]
        cones = np.array([
            [480, 250, 40, 60],  # 左侧障碍物
            [520, 250, 40, 60],  # 右侧障碍物
        ])
        
        # 测试障碍物检测
        cones_processed = np.concatenate((
            cones[:, 0:1] + cones[:, 2:3] / 2,  # x_center
            cones[:, 1:2] + cones[:, 3:4],      # y_bottom
            cones[:, 2:3],                      # width
        ), axis=1)
        
        # 检测阻挡路径的障碍物
        blocking_cones = cones_processed[
            np.abs(cones_processed[:, 0] - target[0]) < cones_processed[:, 2] * self.config.SAFETY_MARGIN
        ]
        
        self.assertGreater(len(blocking_cones), 0)
    
    def test_avoidance_direction(self):
        """测试避障方向判断"""
        target = np.array([500, 300])
        
        # 左侧障碍物
        left_cone = np.array([480, 310, 40])  # [x_center, y_bottom, width]
        obstacle_side = "LEFT" if left_cone[0] < target[0] else "RIGHT"
        self.assertEqual(obstacle_side, "LEFT")
        
        # 右侧障碍物
        right_cone = np.array([520, 310, 40])
        obstacle_side = "LEFT" if right_cone[0] < target[0] else "RIGHT"
        self.assertEqual(obstacle_side, "RIGHT")
    
    def test_target_boundary_check(self):
        """测试目标点边界检查"""
        # 测试左边界
        target = np.array([50, 300])
        clipped_target = np.clip(target[0], self.config.IMAGE_WIDTH_MIN, self.config.IMAGE_WIDTH_MAX)
        self.assertEqual(clipped_target, self.config.IMAGE_WIDTH_MIN)
        
        # 测试右边界
        target = np.array([1200, 300])
        clipped_target = np.clip(target[0], self.config.IMAGE_WIDTH_MIN, self.config.IMAGE_WIDTH_MAX)
        self.assertEqual(clipped_target, self.config.IMAGE_WIDTH_MAX)
    
    def test_smooth_return_calculation(self):
        """测试平滑回正计算"""
        original_target = np.array([500, 300])
        current_target = np.array([600, 300])
        
        # 模拟回正步骤
        for step in range(1, 10):
            alpha = min(step * self.config.RETURN_SMOOTHNESS, 1.0)
            smooth_target = (1 - alpha) * current_target + alpha * original_target
            
            # 验证插值结果在合理范围内
            self.assertGreaterEqual(smooth_target[0], min(original_target[0], current_target[0]))
            self.assertLessEqual(smooth_target[0], max(original_target[0], current_target[0]))
            
            if alpha >= 1.0:
                np.testing.assert_array_almost_equal(smooth_target, original_target)
                break


def run_avoidance_simulation():
    """运行避障仿真测试"""
    print("🚗 智能避障算法仿真测试")
    print("=" * 50)
    
    # 创建避障实例
    avoid = AdvancedAvoid()
    config = AvoidanceConfig()
    
    # 测试场景1：左侧障碍物
    print("\n📍 场景1：左侧障碍物避障")
    target = np.array([500, 300])
    cone = np.array([480, 310, 40])  # [x_center, y_bottom, width]
    
    obstacle_side = "LEFT" if cone[0] < target[0] else "RIGHT"
    avoid_offset = cone[2] * config.AVOID_DISTANCE_FACTOR
    new_target = np.array([cone[0] + avoid_offset, target[1]])
    
    print(f"原目标: {target}")
    print(f"障碍物: {cone}")
    print(f"避障方向: {obstacle_side}")
    print(f"新目标: {new_target}")
    
    # 测试场景2：右侧障碍物
    print("\n📍 场景2：右侧障碍物避障")
    cone = np.array([520, 310, 40])
    
    obstacle_side = "LEFT" if cone[0] < target[0] else "RIGHT"
    avoid_offset = cone[2] * config.AVOID_DISTANCE_FACTOR
    new_target = np.array([cone[0] - avoid_offset, target[1]])
    
    print(f"原目标: {target}")
    print(f"障碍物: {cone}")
    print(f"避障方向: {obstacle_side}")
    print(f"新目标: {new_target}")
    
    # 测试场景3：平滑回正
    print("\n📍 场景3：平滑回正仿真")
    original_target = np.array([500, 300])
    current_target = np.array([600, 300])
    
    print(f"原始目标: {original_target}")
    print(f"当前目标: {current_target}")
    print("回正过程:")
    
    for step in range(1, 8):
        alpha = min(step * config.RETURN_SMOOTHNESS, 1.0)
        smooth_target = (1 - alpha) * current_target + alpha * original_target
        print(f"  步骤{step}: alpha={alpha:.2f}, 目标={smooth_target}")
        
        if alpha >= 1.0:
            break
    
    print("\n✅ 仿真测试完成！")


if __name__ == "__main__":
    # 运行单元测试
    print("🧪 运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行仿真测试
    run_avoidance_simulation()
