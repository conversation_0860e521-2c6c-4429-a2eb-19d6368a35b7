{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "e:/2024智能汽车竟赛-地平线智慧医疗/2024智能汽车竞赛-地平线智慧医疗/SDCard_19/ahpu_ws/src/hobot_packages/hobot_dnn-foxy/dnn_node_sample/src", "program": "e:/2024智能汽车竟赛-地平线智慧医疗/2024智能汽车竞赛-地平线智慧医疗/SDCard_19/ahpu_ws/src/hobot_packages/hobot_dnn-foxy/dnn_node_sample/src/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}