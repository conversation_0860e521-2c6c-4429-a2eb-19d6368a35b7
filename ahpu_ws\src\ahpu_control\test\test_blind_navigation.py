#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
盲飞导航测试文件
测试在什么都检测不到时的应急策略
"""

import sys
import os
import numpy as np
import time
from unittest.mock import Mock, patch

# 添加路径以导入控制模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'ahpu_control'))

def test_blind_navigation_scenarios():
    """测试各种盲飞导航场景"""
    print("🚁 盲飞导航场景测试")
    print("=" * 50)
    
    # 模拟避障实例
    with patch('rclpy.node.Node'):
        from control_yolov5 import AdvancedAvoid, AvoidanceConfig
        avoid = AdvancedAvoid()
        config = AvoidanceConfig()
    
    # 场景1：有最后有效目标点
    print("\n📍 场景1：基于最后有效目标点的盲飞")
    avoid.last_valid_target = np.array([500, 300])
    avoid.avoid_state = "NORMAL"
    
    # 模拟盲飞导航
    class MockController:
        def __init__(self):
            self.logger = Mock()
            
        def blind_navigation(self):
            current_time = time.time()
            
            if avoid.avoid_state != "BLIND_NAVIGATION":
                avoid.avoid_state = "BLIND_NAVIGATION"
                avoid.blind_start_time = current_time
                avoid.blind_navigation_count += 1
                print(f"🚁 启动盲飞导航 (第{avoid.blind_navigation_count}次)")
            
            if avoid.last_valid_target is not None:
                target = avoid.last_valid_target.copy()
                print(f"🎯 盲飞目标: 最后有效点 {target}")
                return target
            
            return np.array([580, 300])
        
        def emergency_recovery(self):
            print("🆘 执行紧急恢复")
            avoid.clear()
            avoid.avoid_state = "NORMAL"
            return np.array([580, 350])
    
    controller = MockController()
    target1 = controller.blind_navigation()
    print(f"返回目标: {target1}")
    
    # 场景2：只有原始目标点
    print("\n📍 场景2：基于原始目标点的盲飞")
    avoid.clear()
    avoid.last_valid_target = None
    avoid.original_target = np.array([480, 280])
    avoid.avoid_state = "NORMAL"
    
    target2 = controller.blind_navigation()
    print(f"返回目标: {target2}")
    
    # 场景3：什么都没有，直行
    print("\n📍 场景3：完全盲飞，保持直行")
    avoid.clear()
    avoid.avoid_state = "NORMAL"
    
    target3 = controller.blind_navigation()
    print(f"返回目标: {target3}")
    
    # 场景4：盲飞超时恢复
    print("\n📍 场景4：盲飞超时，紧急恢复")
    avoid.avoid_state = "BLIND_NAVIGATION"
    avoid.blind_start_time = time.time() - config.MAX_BLIND_TIME - 1  # 模拟超时
    
    recovery_target = controller.emergency_recovery()
    print(f"恢复目标: {recovery_target}")
    print(f"状态重置: {avoid.avoid_state}")


def simulate_detection_failure():
    """模拟检测失败的完整流程"""
    print("\n🎬 检测失败完整流程仿真")
    print("=" * 50)
    
    # 模拟时间序列
    timeline = [
        {"time": 0, "detection": "normal", "targets": ["line"], "desc": "正常检测地标线"},
        {"time": 1, "detection": "normal", "targets": ["line"], "desc": "继续正常检测"},
        {"time": 2, "detection": "failed", "targets": [], "desc": "检测失败！启动盲飞"},
        {"time": 3, "detection": "failed", "targets": [], "desc": "继续盲飞"},
        {"time": 4, "detection": "failed", "targets": [], "desc": "盲飞中..."},
        {"time": 5, "detection": "recovered", "targets": ["line"], "desc": "检测恢复"},
    ]
    
    # 模拟状态
    last_valid_target = np.array([500, 300])
    current_state = "NORMAL"
    blind_start_time = None
    
    for step in timeline:
        print(f"\n⏰ 时间 {step['time']}s: {step['desc']}")
        
        if step['detection'] == "normal":
            # 正常检测
            current_target = np.array([500 + np.random.randint(-20, 20), 300])
            last_valid_target = current_target.copy()
            current_state = "NORMAL"
            print(f"   ✅ 检测目标: {current_target}")
            print(f"   📝 更新最后有效目标: {last_valid_target}")
            
        elif step['detection'] == "failed":
            # 检测失败
            if current_state != "BLIND_NAVIGATION":
                current_state = "BLIND_NAVIGATION"
                blind_start_time = step['time']
                print(f"   🚁 启动盲飞导航")
            
            # 盲飞策略
            if last_valid_target is not None:
                blind_target = last_valid_target.copy()
                print(f"   🎯 盲飞目标: {blind_target} (基于最后有效点)")
            else:
                blind_target = np.array([580, 300])
                print(f"   ➡️ 盲飞目标: {blind_target} (直行)")
            
            # 检查超时
            if step['time'] - blind_start_time > 2.0:  # MAX_BLIND_TIME
                print(f"   ⚠️ 盲飞超时，准备紧急恢复")
                
        elif step['detection'] == "recovered":
            # 检测恢复
            current_target = np.array([495, 305])
            last_valid_target = current_target.copy()
            current_state = "NORMAL"
            print(f"   🎉 检测恢复: {current_target}")
            print(f"   🔄 状态重置为: {current_state}")


def analyze_blind_navigation_safety():
    """分析盲飞导航的安全性"""
    print("\n🛡️ 盲飞导航安全性分析")
    print("=" * 50)
    
    safety_measures = [
        {
            "measure": "时间限制",
            "description": "最大盲飞时间2秒，防止长时间盲飞",
            "config": "MAX_BLIND_TIME = 2.0"
        },
        {
            "measure": "速度限制", 
            "description": "盲飞时降低速度到0.4，提高安全性",
            "config": "BLIND_SPEED = 0.4"
        },
        {
            "measure": "角度限制",
            "description": "盲飞时限制转角±15度，避免急转",
            "config": "BLIND_ANGLE_LIMIT = 15.0"
        },
        {
            "measure": "目标记忆",
            "description": "记住最后有效目标点，优先使用",
            "config": "保存 last_valid_target"
        },
        {
            "measure": "紧急恢复",
            "description": "超时后强制恢复到安全状态",
            "config": "emergency_recovery()"
        }
    ]
    
    for i, measure in enumerate(safety_measures, 1):
        print(f"{i}. {measure['measure']}")
        print(f"   📋 {measure['description']}")
        print(f"   ⚙️ {measure['config']}")
        print()


def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 边界情况测试")
    print("=" * 50)
    
    edge_cases = [
        {
            "case": "连续检测失败",
            "scenario": "长时间无法检测到任何目标",
            "expected": "多次启动盲飞，最终紧急恢复"
        },
        {
            "case": "避障中检测失败",
            "scenario": "正在避障时突然检测失败",
            "expected": "保持避障状态，使用原始目标点"
        },
        {
            "case": "回正中检测失败",
            "scenario": "正在回正时突然检测失败",
            "expected": "继续回正到原始目标点"
        },
        {
            "case": "目标点异常",
            "scenario": "检测到的目标点超出图像边界",
            "expected": "使用边界限制，确保安全"
        }
    ]
    
    for i, case in enumerate(edge_cases, 1):
        print(f"{i}. {case['case']}")
        print(f"   🎭 场景: {case['scenario']}")
        print(f"   ✅ 预期: {case['expected']}")
        print()


if __name__ == "__main__":
    # 运行所有测试
    test_blind_navigation_scenarios()
    simulate_detection_failure()
    analyze_blind_navigation_safety()
    test_edge_cases()
    
    print("\n🎉 盲飞导航测试完成！")
    print("\n💡 使用建议:")
    print("1. 在实际测试中监控盲飞触发频率")
    print("2. 根据场地情况调整盲飞参数")
    print("3. 确保摄像头和检测算法的稳定性")
    print("4. 在复杂环境中测试盲飞效果")
