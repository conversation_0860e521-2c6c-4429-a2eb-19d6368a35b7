#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple fix for UTF-8 encoding issues - only replace critical Chinese characters
"""

def simple_fix():
    file_path = "src/ahpu_control/ahpu_control/control_yolov5.py"
    
    # Read file with error handling
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()
    
    # Only replace the most critical Chinese characters that cause compilation errors
    critical_replacements = {
        '运动控制节点启动成功': 'Motion control node started successfully',
        '当前是停车状态': 'Currently in parking state',
        '检测目标数量': 'Detected targets',
        '线路数量': 'Lines',
        '圆锥数量': 'Cones', 
        '停车区域数量': 'Parking areas',
        '二维码数量': 'QR codes',
        '进入任务1': 'Entering task 1',
        '进入任务2': 'Entering task 2',
        '进入任务3': 'Entering task 3',
        '进入模型图生文': 'Entering model image-to-text',
        '停车重置': 'Parking reset',
        '保完赛': 'Race completed',
        '检测到二维码，前往二维码': 'QR code detected, moving to QR code',
        '二维码距离很近，停车识别': 'QR code very close, stop for recognition',
        '未找到二维码，前往地标线': 'QR code not found, moving to landmark line',
        '目标点坐标': 'Target coordinates',
        '速度': 'Speed',
        '角度': 'Angle',
    }
    
    # Apply only critical replacements
    for chinese, english in critical_replacements.items():
        content = content.replace(chinese, english)
    
    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Simple UTF-8 fix completed!")
    print("Critical Chinese characters have been replaced.")

if __name__ == "__main__":
    simple_fix()
