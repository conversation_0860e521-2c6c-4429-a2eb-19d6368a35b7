#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Fix encoding issues by replacing Chinese comments with English
"""

import re

def fix_encoding():
    file_path = "src/ahpu_control/ahpu_control/control_yolov5.py"
    
    # Read the file with UTF-8 encoding
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        # If UTF-8 fails, try with error handling
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()
    
    # Replace Chinese comments with English equivalents
    replacements = {
        # Common Chinese comments
        "# 任务1策略代码": "# Task 1 strategy code",
        "# 优化版：智能目标搜索": "# Optimized version: intelligent target search",
        "# 二维码检测（最高优先级）": "# QR code detection (highest priority)",
        "# 地标线跟踪": "# Landmark line tracking",
        "# 只有锥桶的情况": "# Only cones situation",
        "# 智能搜索二维码": "# Intelligent QR code search",
        "# 什么都检测不到": "# Nothing detected",
        "# 盲飞导航": "# Blind navigation",
        "# 更新最后有效目标点": "# Update last valid target point",
        "# 使用智能避障算法": "# Use intelligent obstacle avoidance algorithm",
        "# 计算控制指令": "# Calculate control commands",
        "# 发布控制指令": "# Publish control commands",
        "# 目标点坐标": "# Target point coordinates",
        "# 速度": "# Speed",
        "# 角度": "# Angle",
        "# 通过拟合曲线拟合得到目标点": "# Get target point through curve fitting",
        "# 准备检测信息（停车任务）": "# Prepare detection info (parking task)",
        "# 停车任务不关心二维码": "# Parking task doesn't care about QR codes",
        "# 使用智能避障算法（停车任务）": "# Use intelligent obstacle avoidance (parking task)",
        "# 使用智能避障算法（停车任务-地标线跟踪）": "# Use intelligent obstacle avoidance (parking-landmark tracking)",
        "# 智能避障算法": "# Intelligent obstacle avoidance algorithm",
        "# 更新避障状态（传入检测信息）": "# Update avoidance state (pass detection info)",
        "# 没有障碍物，重置避障状态": "# No obstacles, reset avoidance state",
        "# 处理圆锥数据": "# Process cone data",
        "# 检测是否有障碍物阻挡路径": "# Check if obstacles block the path",
        "# 路径清晰，执行智能回正逻辑": "# Path clear, execute intelligent return logic",
        "# 检查是否有实时地标线信息": "# Check for real-time landmark line info",
        "# 选择最近的障碍物": "# Select nearest obstacle",
        "# 判断避障方向": "# Determine avoidance direction",
        "# 障碍物在左侧，向右避障": "# Obstacle on left, avoid right",
        "# 障碍物在右侧，向左避障": "# Obstacle on right, avoid left",
        "# 计算避障目标点": "# Calculate avoidance target point",
        "# 边界检查，确保目标点在合理范围内": "# Boundary check, ensure target in reasonable range",
        "# 图像宽度范围": "# Image width range",
        "# 更新避障状态": "# Update avoidance state",
        "# 智能回正策略": "# Intelligent return strategy",
        "# 优先寻找实时地标线": "# Prioritize finding real-time landmark lines",
        "# 还在避障中，继续当前策略": "# Still avoiding, continue current strategy",
        "# 执行智能回正策略": "# Execute intelligent return strategy",
        "# 策略1：如果能看到地标线，优先跟随实时地标线": "# Strategy 1: If landmark lines visible, prioritize real-time lines",
        "# 使用实时检测到的地标线": "# Use real-time detected landmark lines",
        "# 最远的地标线": "# Farthest landmark line",
        "# 找到地标线，回正完成": "# Found landmark line, return complete",
        "# 策略2：没有地标线，向图像中心区域搜索": "# Strategy 2: No landmark lines, search center area",
        "# 向图像中心偏下的区域移动，这里通常是地标线的可能位置": "# Move to lower center area, usually possible landmark line position",
        "# 图像中心偏下": "# Lower center of image",
        "# 如果有原始目标点作为参考，进行方向性搜索": "# If original target as reference, directional search",
        "# 根据原始位置调整搜索方向": "# Adjust search direction based on original position",
        "# 原来在左侧": "# Originally on left",
        "# 向左搜索": "# Search left",
        "# 原来在右侧": "# Originally on right",
        "# 向右搜索": "# Search right",
        "# 搜索超时处理": "# Search timeout handling",
        "# 约3秒搜索时间": "# About 3 seconds search time",
        "# 返回中心位置": "# Return to center position",
    }
    
    # Apply replacements
    for chinese, english in replacements.items():
        content = content.replace(chinese, english)
    
    # Remove any remaining non-ASCII characters in comments
    # This regex finds Chinese characters in comments and replaces them
    content = re.sub(r'#[^#\n]*[\u4e00-\u9fff]+[^#\n]*', lambda m: '# [Chinese comment removed]', content)
    
    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Encoding fix completed!")

if __name__ == "__main__":
    fix_encoding()
