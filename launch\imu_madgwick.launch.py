from launch import LaunchDescription
from launch_ros.actions import Node

def generate_launch_description():
    return LaunchDescription([
        Node(
            package='ahpu_imu',
            executable='imu_processor_madgwick',
            name='imu_processor_madgwick',
            parameters=[{
                'sample_frequency': 100.0,  # IMU采样频率
                'gain': 0.1,                # Madgwick滤波器增益
                'calibration_time': 2.0,    # 校准时间(秒)
                'use_relative_heading': True, # 使用相对航向角
                'heading_output_interval': 0.2, # 航向角输出间隔(秒)
                'significant_change': 0.5     # 有意义的角度变化阈值(度)
            }],
            output='screen'
        )
    ]) 