from launch import LaunchDescription
from launch_ros.actions import Node


def generate_launch_description():
    return LaunchDescription(
        [
            Node(
                package="ahpu_control",
                executable="control_yolov5",
                name="control_yolov5",
            ),
            Node(package="ahpu_control", executable="car_state", name="car_state"),
        ]
    )
