<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>hobot_usb_cam_foxy</name>
  <version>2.1.2</version>
  <description>TogetheROS hobot usb camera</description>
  <maintainer email="<EMAIL>">kairui</maintainer>
  <license>Apache License 2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>std_msgs</depend>
  <depend>std_srvs</depend>
  <depend>sensor_msgs</depend>
  <depend>hbm_img_msgs</depend>
  <depend>v4l-utils</depend>
  <depend>yaml_cpp_vendor</depend>
  <!-- Only required for MJPEG to RGB converison -->
  <depend>ffmpeg</depend>
  <member_of_group>rosidl_interface_packages</member_of_group>
  
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
