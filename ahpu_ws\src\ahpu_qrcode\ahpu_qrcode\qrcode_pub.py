# -*- coding: utf-8 -*-
import rclpy
from rclpy.node import Node
from rclpy.logging import get_logger

from std_msgs.msg import Int32
from ahpu_interfaces.msg import Sign
from sensor_msgs.msg import CompressedImage

from cv_bridge import CvBridge
import cv2
import time

from threading import Lock, Thread


class QRCodeDetectionPub(Node):
    def __init__(self, name):
        super().__init__(name)
        
        self.lock = Lock()
        self.image = None

        self.declare_parameter("image_topic", "/image")
        self.image_topic = self.get_parameter("image_topic").value

        self.logger = get_logger(self.get_name())  # 日志记录器
        self.state = 0  # 默认是停车状态

        try:
            self.wechat_detector = cv2.wechat_qrcode_WeChatQRCode(
                "/root/ahpu_config/WeChatQRCode/detect.prototxt",
                "/root/ahpu_config/WeChatQRCode/detect.caffemodel",
                "/root/ahpu_config/WeChatQRCode/sr.prototxt",
                "/root/ahpu_config/WeChatQRCode/sr.caffemodel",
            )
        except Exception as e:
            self.logger.error(f"初始化WeChatQRCode检测器失败: {e}")
            raise

        self.cv_bridge = CvBridge()

        # 订阅原摄像头发布的图像话题
        self.subscription_ = self.create_subscription(
            CompressedImage, self.image_topic, self.image_callback, 1
        )

        # 创建发布者，向foxglove发布二维码消息
        self.pub_sign_switch = self.create_publisher(Sign, "sign_switch", 1)

        # 创建订阅者，订阅二维码识别情况      发布者，发布二维码识别情况
        self.pub_qr_code = self.create_publisher(Int32, "/qr_data", 1)

        # 创建订阅者，订阅小车状态节点
        self.sub_car_state = self.create_subscription(
            Int32, "/car_state", self.sub_car_state_callback, 1
        )
        
        self.subscription_
        self.pub_sign_switch
        self.pub_qr_code
        self.sub_car_state

        self.init()

    def init(self):
        self.logger.info(f"QRcode Detection 启动成功")
        if self.state == 0:
            self.logger.info("当前为停车状态")

    def image_callback(self, msg):
        if self.state in [0, 2, 3]:
            time.sleep(0.100)
            return

        self.logger.info(f"正在识别二维码内容")
        cv_image = self.cv_bridge.compressed_imgmsg_to_cv2(msg)
        barcodes, _ = self.wechat_detector.detectAndDecode(cv_image)
        if len(barcodes) == 0:
            self.logger.info(f"未识别到二维码内容")
            return
        res = search_in_qrcode_wechat(barcodes)
        if res is None:
            return
        self.pub_qr_code.publish(Int32(data=res))  # 发布qr_data
        self.pub_sign_switch_callback(res)  # 发布qr_data给foxglove
        self.logger.info(f"识别到二维码: {res}")

        self.state = 2  # 识别到二维码，切换至手动状态

    def pub_sign_switch_callback(self, res):
        sign = Sign()
        sign.sign_data = res
        self.pub_sign_switch.publish(sign)
        self.pub_sign_switch.publish(sign)
        self.pub_sign_switch.publish(sign)

    def sub_car_state_callback(self, msg):
        self.state = msg.data
        if self.state == 0:
            self.logger.info("停车重置状态")
        elif self.state == 1:
            self.logger.info("进入任务1")
        elif self.state == 2:
            self.logger.info("进入任务2")
        elif self.state == 3:
            self.logger.info("进入任务3")


def search_in_qrcode_wechat(barcodes):
    for barcode in barcodes:
        if barcode == "ClockWise":
            return 3
        elif barcode == "AntiClockWise":
            return 4
        else:
            continue
    return None


def main(args=None):
    rclpy.init(args=args)
    qr_code_detection_pub = QRCodeDetectionPub("qr_code_pub")
    try:
        rclpy.spin(qr_code_detection_pub)
    except KeyboardInterrupt:
        pass
    qr_code_detection_pub.destroy_node()
    rclpy.shutdown()


if __name__ == "__main__":
    main()
