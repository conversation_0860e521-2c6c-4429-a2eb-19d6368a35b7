import rclpy
from rclpy.node import Node

from sensor_msgs.msg import CompressedImage, Image

from cv_bridge import CvBridge
import cv2
import os
import time


class VideoRecord(Node):

    def __init__(self):
        super().__init__("video_record")
        self.init()

        self.bridge = CvBridge()
        self.video_writer = cv2.VideoWriter()
        self.video_writer.open(
            self.video_path,
            cv2.VideoWriter_fourcc(*"XVID"),
            self.video_fps,
            (self.video_width, self.video_height),
        )
        self.subscription = self.create_subscription(
            CompressedImage, self.sub_topic, self.image_callback, 10
        )
        self.subscription  # prevent unused variable warning

    def init(self):
        self.count = 0

        self.declare_parameter("video_dir", os.environ.get("HOME"))
        self.declare_parameter("sub_topic", "/image")
        self.declare_parameter("video_width", 640)
        self.declare_parameter("video_height", 480)
        self.declare_parameter("video_fps", 30)

        self.video_dir = self.get_parameter("video_dir").value
        self.sub_topic = self.get_parameter("sub_topic").value
        self.video_width = self.get_parameter("video_width").value
        self.video_height = self.get_parameter("video_height").value
        self.video_fps = self.get_parameter("video_fps").value

        self.video_path = os.path.join(
            self.get_parameter("video_dir").value,
            time.strftime("%Y%m%d_%H%M%S") + ".avi",
        )

        self.get_logger().info(
            f"Video Recording to {self.video_path}\nSubscribing to {self.sub_topic}\nVideo Resolution: {self.video_width}x{self.video_height}\nVideo FPS: {self.video_fps}"
        )

    def image_callback(self, msg):
        cv_image = self.bridge.compressed_imgmsg_to_cv2(
            msg, desired_encoding="passthrough"
        )
        self.count += 1
        self.get_logger().info(f"Received Image {self.count}")
        self.video_writer.write(cv_image)


def main(args=None):
    rclpy.init(args=args)

    video_record = VideoRecord()
    try:
        rclpy.spin(video_record)
    except KeyboardInterrupt:
        video_record.get_logger().info("Keyboard Interrupt (SIGINT)")
    finally:
        video_record.video_writer.release()
        video_record.get_logger().info("Video Recording Finished")

    video_record.destroy_node()
    rclpy.shutdown()
