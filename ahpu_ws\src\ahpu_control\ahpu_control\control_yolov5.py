import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile
from std_msgs.msg import Int32, Float32
from ai_msgs.msg import PerceptionTargets
from geometry_msgs.msg import Twist
from rclpy.logging import get_logger
from rclpy.executors import SingleThreadedExecutor, MultiThreadedExecutor
from rclpy.callback_groups import MutuallyExclusiveCallbackGroup, ReentrantCallbackGroup
from threading import Lock
from scipy.optimize import curve_fit

import numpy as np
import time
import time


class NavigationState:
    def __init__(self):
        # 状态管理
        self.state = "NORMAL"  # NORMAL, AVOIDING, RETURNING
        self.avoid_start_time = None
        self.avoid_direction = None  # LEFT, RIGHT

        # 航向角相关
        self.original_heading = None  # 避障前的航向角
        self.current_heading = None   # 当前航向角
        self.target_heading = 18.0    # 目标航向角（二维码方向，可调整）
        self.turning_direction = None # 记录当前转向方向：RIGHT_TO_0 或 LEFT_TO_90

        # 锥桶避障参数
        self.cone_y_threshold = 350  # 锥桶Y坐标阈值
        self.avoid_timeout = 1     # 避障超时时间(秒)
        self.avoid_y_threshold = 360 # 避障触发的Y坐标阈值（锥桶必须足够近）

        # 拟合参数
        self.fit_y_distance = 320    # 增大Y值看得更jin

    def start_avoiding(self, direction, heading=None):
        """开始避障"""
        self.state = "AVOIDING"
        self.avoid_start_time = time.time()
        self.avoid_direction = direction
        # 保存避障前的航向角
        if heading is not None:
            self.original_heading = heading
        elif self.current_heading is not None:
            self.original_heading = self.current_heading

    def should_return(self):
        """检查是否应该进入回正状态"""
        if self.state == "AVOIDING" and self.avoid_start_time:
            return time.time() - self.avoid_start_time > self.avoid_timeout
        return False

    def start_returning(self):
        """开始回正"""
        self.state = "RETURNING"

    def reset_to_normal(self):
        """重置到正常状态"""
        self.state = "NORMAL"
        self.avoid_start_time = None
        self.avoid_direction = None
        self.original_heading = None

nav_state = NavigationState()


class Curve_fitter:
    def __init__(self):
        pass

    def __call__(self, lines_center: np.ndarray, y: int) -> np.ndarray:
        if len(lines_center) == 0:  # 没有轨迹线
            return np.array([740, y])

        elif len(lines_center) == 1:  # 只有一个轨迹线，无法拟合，直接返回
            return lines_center

        # elif len(lines_center) == 2:  #  有两个轨迹线，线性拟合
        #     x_data = lines_center[:, 0]
        #     y_data = lines_center[:, 1]

        #     popt, pcov = curve_fit(self.cuuve_model_linear, y_data, x_data, p0=[1, 1])

        #     target_x, target_y = self.cuuve_model_linear(y, *popt), y

        #     return np.array([target_x, target_y])
        elif len(lines_center) == 2:  #  有两个轨迹线，返回最远的那个
            line_max = lines_center[np.argmin(lines_center[:, 1])]
            
            return line_max      

        else:  #  有多个轨迹线，曲线拟合
            x_data = lines_center[:, 0]
            y_data = lines_center[:, 1]

            popt, pcov = curve_fit(
                self.cruve_model_quadratic, y_data, x_data, p0=[1, 1, 1]
            )

            target_x, target_y = self.cruve_model_quadratic(y, *popt), y

            return np.array([target_x, target_y])

    @staticmethod
    def cruve_model_quadratic(x, a, b, c):
        return a * x**2 + b * x + c

    @staticmethod
    def cuuve_model_linear(x, a, b):
        return a * x + b


fitter = Curve_fitter()


class Control(Node):
    def __init__(self, name):
        super().__init__(name)  # type: ignore

        self.callback_group = ReentrantCallbackGroup()
        self.logger = get_logger(self.get_name())
        self.lock = Lock()  # 线程锁
        self.state = 0  # 默认是停车状态
        self.stop_state = False
        self.targets = {}  # 存储yolov5的检测结果
        self.reset_targets()  # 初始化targets
        self.arrive_park = 0
        self.arrive_qrcode = 0

        self.init()

        # 创建订阅者，订阅Yolov5的检测结果
        self.sub_yolov5 = self.create_subscription(
            PerceptionTargets,
            self.yolov5_topic,  # type: ignore
            self.sub_yolov5_callback,
            5,
            callback_group=self.callback_group,
        )

        # 创建发布者，发布控制指令
        self.pub_cmd_vel = self.create_publisher(
            Twist, "/cmd_vel", 1, callback_group=self.callback_group
        )

        # 创建订阅者，订阅航向角
        self.sub_heading = self.create_subscription(
            Float32,
            "/stabilized_heading",
            self.heading_callback,
            10,
            callback_group=self.callback_group,
        )

        # 创建订阅者，订阅小车的状态
        self.sub_car_state = self.create_subscription(
            Int32,
            "/car_state",
            self.sub_car_state_callback,
            1,
            callback_group=self.callback_group,
        )

        # 创建定时器
        self.control_timer = self.create_timer(
            1 / 25, self.control, callback_group=self.callback_group
        )

    def init(self):
        self.declare_parameter("yolov5_topic", "/dnn_node_sample")
        self.yolov5_topic = self.get_parameter("yolov5_topic").value

        self.logger.info("运动控制节点启动成功")
        if self.state == 0:
            self.logger.info("当前是停车状态")

    def sub_yolov5_callback(self, msg):
        # 订阅yolov5的检测结果, 更新targets
        cones, lines, park, qrcode = [], [], [], []
        if len(msg.targets) > 0:
            for target in msg.targets:
                if target.type == "line":
                    lines.append(self.get_bboxes(target.rois))
                elif target.type == "cone":
                    cones.append(self.get_bboxes(target.rois))
                elif target.type == "park":
                    park.append(self.get_bboxes(target.rois))
                elif target.type == "qrcode":
                    qrcode.append(self.get_bboxes(target.rois))
            with self.lock:
                self.targets = {
                    "qrcode": qrcode,
                    "park": park,
                    "lines": lines,
                    "cones": cones,
                }
        # self.logger.info(f"{self.targets}")
        #self.logger.info(
        #    f"检测目标数量：{len(msg.targets)}, 线路数量：{len(lines)}, 圆锥数量：{len(cones)}, 停车区域数量：{len(park)}, 二维码数量：{len(qrcode)}"
        #)

    def heading_callback(self, msg):
        """航向角回调函数"""
        nav_state.current_heading = msg.data
        # self.logger.info(f"当前航向角: {msg.data:.2f}°")

    def sub_car_state_callback(self, msg):
        with self.lock:
            self.state = msg.data
        if msg.data == 1:
            self.logger.info("进入任务1")
            self.arrive_qrcode = 0
            self.arrive_park = 0
        elif msg.data == 2:
            self.logger.info("进入任务2")
            self.arrive_qrcode = 999
            self.arrive_park = 0
        elif msg.data == 3:
            self.logger.info("进入任务3")
            self.arrive_qrcode = 999
            self.arrive_park = 0
        elif msg.data == 8:
            self.logger.info("进入模型图生文")
        elif msg.data == 0:
            self.logger.info("停车重置")
            self.arrive_qrcode = 0
            self.arrive_park = 0
        elif msg.data == 9:
            self.logger.info("保完赛")
            self.arrive_qrcode = 999
            self.arrive_park = 999
        self.stop()
        with self.lock:
            self.stop_state = False

    def control(self):
        with self.lock:
            state = self.state

        if state == 0:  # 停车重置
            with self.lock:
                stop_state = self.stop_state
            if not stop_state:
                self.stop()
                with self.lock:
                    self.stop_state = True

        elif state == 1:  # 执行任务1
            with self.lock:
                targets = self.targets
            self.reset_targets()  # 获取后清空，避免重复
            self.action_task1(targets)  # 任务1策略代码

        elif state == 2:  # 执行任务2
            with self.lock:
                stop_state = self.stop_state
            if not stop_state:
                self.stop()
                with self.lock:
                    self.stop_state = True

        elif state == 3:  # 执行任务3
            with self.lock:
                targets = self.targets
            self.reset_targets()  # 获取后清空，避免重复
            self.action_task2(targets)  # 任务2策略代码
        
        elif state == 9:  # 执行任务3
            self.set_speed(0.4, 0.0)
            time.sleep(1.500)
            self.set_speed(-0.4, 0.0)
            time.sleep(1.400)
            self.stop()
            with self.lock:
                self.state = 0
            self.stop()
            time.sleep(300.000)

    def action_task1(self, targets: dict):
        # 任务1策略代码 - 基于状态机的导航

        qrcode, cones, lines = (
            np.array(targets["qrcode"]).reshape(-1, 4),
            np.array(targets["cones"]).reshape(-1, 4),
            np.array(targets["lines"]).reshape(-1, 4),
        )  # [[x, y, w, h], [x, y, w, h], ...]
        self.logger.info(f"二维码:{len(qrcode)},锥桶:{len(cones)},地标线:{len(lines)}")
        # 状态机逻辑
        self.update_navigation_state(qrcode, cones, lines)

        # 根据当前状态执行相应策略
        if nav_state.state == "AVOIDING":
            target = self.execute_avoiding_strategy(cones)
        elif nav_state.state == "RETURNING":
            target = self.execute_returning_strategy(qrcode, lines)
        else:  # NORMAL
            target = self.execute_normal_strategy(qrcode, cones, lines)

        # 计算控制指令并执行
        if target is not None:
            angle, vel = self.calculate_control(target)
            self.set_speed(vel, angle)
            self.logger.info(f"状态:{nav_state.state}, 目标:{target}, 速度:{vel:.2f}, 角度:{angle:.2f}")
        else:
            # 停车等待
            self.set_speed(0.0, 0.0)
            self.logger.info("停车等待")

    def update_navigation_state(self, qrcode, cones, lines):
        """更新导航状态"""
        # 检查是否应该从避障转为回正
        if nav_state.should_return():
            nav_state.start_returning()
            self.logger.info("避障超时，进入回正状态")

        # 如果在回正状态且检测到二维码或地标线，重置为正常状态
        if nav_state.state == "RETURNING" and (len(qrcode) > 0 or len(lines) > 0):
            nav_state.reset_to_normal()
            self.logger.info("检测到目标，重置为正常状态")

    def execute_normal_strategy(self, qrcode, cones, lines):
        """正常状态策略：优先级 二维码 > 地标线 > 锥桶"""
        # 优先级1：二维码
        if len(qrcode) > 0:
            return self.handle_qrcode(qrcode, cones)

        # 优先级2：地标线
        elif len(lines) > 0:
            return self.handle_lines(lines, cones)

        # 优先级3：只有锥桶
        elif len(cones) > 0:
            return self.handle_cones_only(cones)

        # 优先级4：什么都没有，使用航向角
        else:
            return self.handle_heading_navigation()

    def handle_qrcode(self, qrcode, cones):
        """处理二维码检测"""
        self.logger.info("检测到二维码，前往二维码")
        qrcode_bottom_center = np.concatenate((
            qrcode[:, 0:1] + qrcode[:, 2:3] / 2,
            qrcode[:, 1:2] + qrcode[:, 3:4],
        ), axis=1)
        target = qrcode_bottom_center[np.argmin(qrcode_bottom_center[:, 1])]

        # 检查是否到达二维码
        if target[1] > 250:
            self.logger.info("二维码距离很近，停车识别")
            self.set_speed(0.0, 0.0)
            self.arrive_qrcode += 1
            return None

        # 检查锥桶阻挡并处理避障
        return self.check_and_avoid_obstacles(target, cones)

    def handle_lines(self, lines, cones):
        """处理地标线检测"""
        self.logger.info("未找到二维码，前往地标线")
        lines_center = lines[:, :2] + lines[:, 2:] / 2
        lines_center = lines_center[lines_center[:, 0] > 300]  # 过滤左侧线

        # 使用更大的Y值进行拟合，看得更近
        target = np.squeeze(fitter(lines_center, nav_state.fit_y_distance))

        # 检查锥桶阻挡并处理避障
        return self.check_and_avoid_obstacles(target, cones)

    def handle_cones_only(self, cones):
        """只检测到锥桶的处理 - 添加Y坐标条件"""
        self.logger.info("只检测到锥桶，判断是否需要避障")

        # 处理锥桶坐标
        cones_processed = np.concatenate((
            cones[:, 0:1] + cones[:, 2:3] / 2,  # x_center
            cones[:, 1:2] + cones[:, 3:4],      # y_bottom
            cones[:, 2:3],                      # width
        ), axis=1)

        # 找到最近的锥桶
        nearest_cone = cones_processed[np.argmax(cones_processed[:, 1])]

        # 添加Y坐标条件：锥桶必须足够近才避障
        if nearest_cone[1] > nav_state.avoid_y_threshold:
            direction = "LEFT" if nearest_cone[0] > 580 else "RIGHT"
            nav_state.start_avoiding(direction)
            self.logger.info(f"锥桶足够近(Y={nearest_cone[1]:.1f})，触发避障，方向: {direction}")

            # 计算避障目标点
            if direction == "LEFT":
                target = np.array([nearest_cone[0] - nearest_cone[2] * 1.3, nearest_cone[1]])
            else:
                target = np.array([nearest_cone[0] + nearest_cone[2] * 1.3, nearest_cone[1]])
            return target
        else:
            # 锥桶距离较远，继续前进
            self.logger.info(f"锥桶距离较远(Y={nearest_cone[1]:.1f})，继续前进")
            return np.array([580, 300])

    def handle_heading_navigation(self):
        """基于航向角的导航 - 什么都检测不到时的处理"""
        if nav_state.current_heading is None:
            self.logger.warn("没有航向角数据，停车等待")
            return None  # 返回None表示停车

        current_angle = nav_state.current_heading
        self.logger.info(f"航向角导航: 当前角度={current_angle:.1f}°")

        # 情况1: 角度大于90° - 开始右转到0°
        if current_angle > 90:
            nav_state.turning_direction = "RIGHT_TO_0"
            self.logger.info(f"角度过大({current_angle:.1f}°),右转至0°")
            return np.array([750, 300])  # 右转

        # 情况2: 角度小于0° - 开始左转到90°
        elif current_angle < -10:
            nav_state.turning_direction = "LEFT_TO_90"
            self.logger.info(f"角度过小({current_angle:.1f}°),左转至90°")
            return np.array([210, 300])  # 左转

        # 情况3: 角度在0-90°之间 - 根据转向方向判断
        else:
            if nav_state.turning_direction == "RIGHT_TO_0":
                # 从>90°右转过来，继续右转直到接近0°
                if current_angle > 5:  # 还没到0°，继续右转
                    self.logger.info(f"继续右转至0°(当前{current_angle:.1f}°)")
                    return np.array([750, 300])
                else:  # 已经接近0°，停止
                    self.logger.info(f"已到达0°附近({current_angle:.1f}°)，停车")
                    nav_state.turning_direction = None
                    return None

            elif nav_state.turning_direction == "LEFT_TO_90":
                # 从<0°左转过来，继续左转直到接近90°
                if current_angle < 85:  # 还没到90°，继续左转
                    self.logger.info(f"继续左转至90°(当前{current_angle:.1f}°)")
                    return np.array([210, 300])
                else:  # 已经接近90°，停止
                    self.logger.info(f"已到达90°附近({current_angle:.1f}°)，停车")
                    nav_state.turning_direction = None
                    return None

            else:
                # 没有转向方向记录，停车等待
                self.logger.info(f"角度在正常范围({current_angle:.1f}°)，停车等待")
                return None

    def execute_avoiding_strategy(self, cones):
        """执行避障策略"""
        self.logger.info(f"执行避障策略，方向: {nav_state.avoid_direction}")

        if len(cones) > 0:
            cones_processed = np.concatenate((
                cones[:, 0:1] + cones[:, 2:3] / 2,
                cones[:, 1:2] + cones[:, 3:4],
                cones[:, 2:3],
            ), axis=1)

            nearest_cone = cones_processed[np.argmax(cones_processed[:, 1])]

            if nav_state.avoid_direction == "LEFT":
                target = np.array([nearest_cone[0] - nearest_cone[2] * 1.3, nearest_cone[1]])
            else:
                target = np.array([nearest_cone[0] + nearest_cone[2] * 1.3, nearest_cone[1]])

            return target
        else:
            # 没有锥桶了，直接进入回正状态
            nav_state.start_returning()
            self.logger.info("避障过程中未检测到锥桶，进入回正状态")
            return np.array([580, 300])  # 返回中心位置

    def execute_returning_strategy(self, qrcode, lines):
        """执行回正策略"""
        self.logger.info("执行回正策略")

        # 如果能看到二维码或地标线，直接跟踪
        if len(qrcode) > 0:
            qrcode_bottom_center = np.concatenate((
                qrcode[:, 0:1] + qrcode[:, 2:3] / 2,
                qrcode[:, 1:2] + qrcode[:, 3:4],
            ), axis=1)
            target = qrcode_bottom_center[np.argmin(qrcode_bottom_center[:, 1])]
            nav_state.reset_to_normal()
            return target

        elif len(lines) > 0:
            lines_center = lines[:, :2] + lines[:, 2:] / 2
            lines_center = lines_center[lines_center[:, 0] > 300]
            target = np.squeeze(fitter(lines_center, nav_state.fit_y_distance))
            nav_state.reset_to_normal()
            return target
        else:
            # 使用航向角回正 - 基于避障前后的角度差值
            if nav_state.original_heading is not None and nav_state.current_heading is not None:
                # 计算角度差值
                angle_diff = nav_state.current_heading - nav_state.original_heading

                # 处理角度跨越180度的情况
                if angle_diff > 180:
                    angle_diff -= 360
                elif angle_diff < -180:
                    angle_diff += 360

                self.logger.info(f"航向角回正: 原始={nav_state.original_heading:.1f}°, 当前={nav_state.current_heading:.1f}°, 差值={angle_diff:.1f}°")

                # 根据角度差值计算回正目标点
                if abs(angle_diff) < 5.0:  # 角度差值小于5度，认为已回正
                    nav_state.reset_to_normal()
                    self.logger.info("航向角回正完成，重置为正常状态")
                    return np.array([580, 300])
                else:
                    # 计算回正目标点 - 回到避障前的航向角
                    correction_factor = min(abs(angle_diff) * 3.0, 150)  # 限制最大偏移150像素
                    if angle_diff > 0:
                        target_x = 580 + correction_factor  
                    else:
                        target_x = 580 - correction_factor

                    # 限制目标点在图像范围内
                    target_x = np.clip(target_x, 200, 960)
                    return np.array([target_x, 300])
            else:
                # 没有航向角数据，返回中心点
                self.logger.warn("缺少航向角数据，无法进行航向角回正")
                return np.array([580, 300])

    def check_and_avoid_obstacles(self, target, cones):
        """检查并处理障碍物 - 添加Y坐标条件"""
        if len(cones) == 0:
            return target

        cones_processed = np.concatenate((
            cones[:, 0:1] + cones[:, 2:3] / 2,
            cones[:, 1:2] + cones[:, 3:4],
            cones[:, 2:3],
        ), axis=1)

        # 检查是否有锥桶阻挡路径
        blocking_cones = cones_processed[
            np.abs(cones_processed[:, 0] - target[0]) < cones_processed[:, 2] * 1.2
        ]

        if len(blocking_cones) > 0:
            nearest_blocking = blocking_cones[np.argmax(blocking_cones[:, 1])]

            # 添加Y坐标条件：只有锥桶足够近才避障
            if nearest_blocking[1] > nav_state.avoid_y_threshold:
                direction = "LEFT" if nearest_blocking[0] > target[0] else "RIGHT"
                nav_state.start_avoiding(direction)
                self.logger.info(f"锥桶足够近(Y={nearest_blocking[1]:.1f})，开始避障，方向: {direction}")

                if direction == "LEFT":
                    return np.array([nearest_blocking[0] - nearest_blocking[2] * 1.3, target[1]])
                else:
                    return np.array([nearest_blocking[0] + nearest_blocking[2] * 1.3, target[1]])
            else:
                # 锥桶还不够近，继续正常行驶
                self.logger.info(f"锥桶距离较远(Y={nearest_blocking[1]:.1f})，继续正常行驶")
                return target

        return target

    def action_task2(self, targets):
        # 任务3策略代码
        if self.arrive_park > 2:
            return
        
        park, cones, lines = (
            np.array(targets["park"]),
            np.array(targets["cones"]),
            np.array(targets["lines"]),
        )
        if len(park) > 0:
            self.logger.info("检测到停车点，前往停车点")
            park_up_center = np.concatenate(
                (
                    park[:, 0:1] + park[:, 2:3] / 2,
                    park[:, 1:2] + park[:, 3:4] / 2,
                ),
                axis=1,
            )  # x+w/2, y+h/2: 停车点的中点坐标
            max_dist_park = park_up_center[
                np.argmin(park_up_center[:, 1])
            ]  # 取y坐标最小的停车点, 即最远的停车点
            target = max_dist_park

            if len(cones) > 0:  # 存在障碍物，检测是否有阻挡前进
                cones = np.concatenate(
                    (
                        cones[:, 0:1] + cones[:, 2:3] / 2,
                        cones[:, 1:2] + cones[:, 3:4],
                        cones[:, 2:3],  # x+w/2, y+h, w
                    ),
                    axis=1,
                )  # x+w/2, y+h, w: 圆锥的下沿中点坐标及宽度
                # 判断是否阻挡前进
                cone = cones[
                    np.abs(cones[:, 0] - target[0]) < cones[:, 2] * 1.0
                ]  # 圆锥的x坐标在目标点左右1.个宽度范围内
                self.get_logger().info(f"障碍物信息: {cone}")
                self.get_logger().info(f"轨迹线信息: {target}")
                if len(cone) > 0:  # 存在障碍物
                    cone = cone[np.argmax(cone[:, 1])]  # 取y坐标最大的圆锥
                    if cone[0] < target[0]:  # 障碍物在目标点左侧
                        target = cone[:2] + np.array(
                            [cone[2] * 1.1, 0.0]
                        )  # 目标点右移1个宽度
                    else:  # 障碍物在目标点右侧
                        target = cone[:2] - np.array(
                            [cone[2] * 1.1, 0.0]
                        )  # 目标点左移1个宽度

            if target[1] > 445:  # 停车点距离很近
                self.set_speed(-1.0, 0.0)
                time.sleep(0.100)
                self.stop()
                self.arrive_park += 1
                return

            angle, vel = self.calculate_control(target)  # 计算控制指令
            self.set_speed(vel, angle)  # 发布控制指令
            self.reset_targets()
            self.logger.info(f"目标点坐标：{target}, 速度：{vel}, 角度：{angle}")

        elif len(lines) > 0:
            self.logger.info("未找到停车点，前往地标线")
            lines_center = (
                lines[:, :2] + lines[:, 2:] / 2
            )  # x+w/2, y+h/2: 地标线中心点坐标
            # lines_center = lines_center[
            #     lines_center[:, 0] > 300
            # ]  # 筛选x坐标大于300的线，尽量过滤左侧地标线
            # target = lines_center[
            #     np.argmin(lines_center[:, 1])
            # ]  # 取y坐标最小的线, 即最远的地标线
            target = np.squeeze(fitter(lines_center, 270))  # 通过拟合曲线拟合得到目标点

            if len(cones) > 0:  # 存在障碍物，检测是否有阻挡前进
                cones = np.concatenate(
                    (
                        cones[:, 0:1] + cones[:, 2:3] / 2,
                        cones[:, 1:2] + cones[:, 3:4],
                        cones[:, 2:3],  # x+w/2, y+h, w
                    ),
                    axis=1,
                )  # x+w/2, y+h, w: 圆锥的下沿中点坐标及宽度
                # 判断是否阻挡前进
                cone = cones[
                    np.abs(cones[:, 0] - target[0]) < cones[:, 2] * 1.2
                ]  # 圆锥的x坐标在目标点左右1.2个宽度范围内
                self.get_logger().info(f"障碍物信息: {cone}")
                self.get_logger().info(f"轨迹线信息: {target}")
                if len(cone) > 0:  # 存在障碍物
                    cone = cone[np.argmax(cone[:, 1])]  # 取y坐标最大的圆锥
                    if (
                        cone[0] - target[0] < -cone[2] * 0.7
                    ):  # 障碍物在目标点左侧, 且距离远
                        target = cone[:2] + np.array(
                            [cone[2] * 1.3, 0.0]
                        )  # 目标点右移1.3个宽度
                    else:  # 障碍物在目标点右侧
                        target = cone[:2] - np.array(
                            [cone[2] * 1.3, 0.0]
                        )  # 目标点左移1.3个宽度

            angle, vel = self.calculate_control(target)  # 计算控制指令
            self.set_speed(vel, angle)  # 发布控制指令
            self.logger.info(f"目标点坐标：{target}, 速度：{vel}, 角度：{angle}")

        else:
            if self.arrive_park < 2:
                self.logger.info("未检测到停车点或地标线，调整姿态")
                time.sleep(0.025)
                self.set_speed(0.6, 0.0)
            else:
                self.logger.info("已到达P点")
                self.stop()

    def avoid_action(self):
        self.logger.info("执行避障动作")
        # 规避动作
        self.set_speed(0.5, 15.0)
        time.sleep(0.3)
        self.set_speed(0.5, -10.0)
        time.sleep(1.6)

    def stop(self):
        self.set_speed(0.0, 0.0)
        self.logger.info("Car Stoped.")

    def calculate_control(self, target):
        # target: [x, y]
        if not target.any():
            return 0.0, 0.0

        # 计算角度（注意处理除零情况）
        if target[1] == 720:
            angle = 90 if target[0] > 580 else -90
        else:
            angle = np.arctan((target[0] - 580) / (target[1] - 720)) * 180 / np.pi

        # 角度控制
        if np.abs(angle) < 5:
            angle_scale = 4.9
        elif np.abs(angle) < 10:
            angle_scale = 4.6
        elif np.abs(angle) < 15:
            angle_scale = 4.3
        elif np.abs(angle) < 20:
            angle_scale = 4.3
        elif np.abs(angle) < 25:
            angle_scale = 4.3
        else:
            angle_scale = 4.3

        # 速度控制
        # if np.abs(angle) < 5:
        #     vel = 1.0
        # elif np.abs(angle) < 10:
        #     vel = 1.0
        # elif np.abs(angle) < 15:
        #     vel = 0.9
        # elif np.abs(angle) < 20:
        #     vel = 0.8
        # elif np.abs(angle) < 25:
        #     vel = 0.7
        # else:
        #     vel = 0.6

        vel = 0.65

        # 角度和速度的缩放
        return angle / angle_scale, vel


    def set_speed(self, x: float, z: float):
        z = np.clip(z, -30.0, 30.0)
        twist_msg = Twist()

        twist_msg.linear.x = x
        twist_msg.linear.y = 0.0
        twist_msg.linear.z = 0.0

        twist_msg.angular.x = 0.0
        twist_msg.angular.y = 0.0
        twist_msg.angular.z = z

        self.pub_cmd_vel.publish(twist_msg)

#从targets中获取每个检测物体的坐标结果
    def get_bboxes(self, rois):
        roi = rois[0]
        x = roi.rect.x_offset
        y = roi.rect.y_offset
        w = roi.rect.width
        h = roi.rect.height
        return [x, y, w, h]

#将数据清除
    def reset_targets(self):
        with self.lock:
            self.targets = {
                "qrcode": [],
                "park": [],
                "lines": [],
                "cones": [],
            }


def main(args=None):
    rclpy.init(args=args)

    control_yolov5 = Control("myh_control_yolov5")

    # 创建执行器，这里使用多线程
    executor = MultiThreadedExecutor()
    executor.add_node(control_yolov5)

    try:
        executor.spin()
    except KeyboardInterrupt:
        pass
    control_yolov5.stop()
    control_yolov5.destroy_node()
    rclpy.shutdown()

if __name__ == "__main__":
    main()
