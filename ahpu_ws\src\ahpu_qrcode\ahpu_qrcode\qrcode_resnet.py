# -*- coding: utf-8 -*-
import rclpy
from rclpy.node import Node
from std_msgs.msg import Int8
from sensor_msgs.msg import Image, CompressedImage
from cv_bridge import CvBridge
import cv2
import numpy as np
import time
from hobot_dnn import pyeasy_dnn as dnn  # type: ignore

_model_path = "/root/zym_ws/src/qr_code_detection/models/resnet18_224x224_nv12.bin"


def bgr2nv12_opencv(image):
    height, width = image.shape[0], image.shape[1]
    area = height * width
    yuv420p = cv2.cvtColor(image, cv2.COLOR_BGR2YUV_I420).reshape((area * 3 // 2,))
    y = yuv420p[:area]
    uv_planar = yuv420p[area:].reshape((2, area // 4))
    uv_packed = uv_planar.transpose((1, 0)).reshape((area // 2,))

    nv12 = np.zeros_like(yuv420p)
    nv12[: height * width] = y
    nv12[height * width :] = uv_packed
    return nv12


def get_hw(pro):
    if pro.layout == "NCHW":
        return pro.shape[2], pro.shape[3]
    else:
        return pro.shape[1], pro.shape[2]


def print_properties(pro):
    print("tensor type:", pro.tensor_type)
    print("data type:", pro.dtype)
    print("layout:", pro.layout)
    print("shape:", pro.shape)


def softmax(x):
    exp_x = np.exp(x)
    return exp_x / np.sum(exp_x)


def postprocess(outputs):
    output = outputs[0].buffer
    prob = np.squeeze(output)
    prob = softmax(prob)
    idx = np.argmax(prob)
    return idx, prob[idx]


class Resnet:
    def __init__(self, model_path):
        self.models = dnn.load(model_path)
        self.classes = ["ClockWise", "AntiClockWise"]
        self.nc = len(self.classes)
        print_properties(self.models[0].inputs[0].properties)
        print_properties(self.models[0].outputs[0].properties)

        self.des_dim = get_hw(self.models[0].inputs[0].properties)
        self.warmup()

    def infer(self, image):
        resized_data = cv2.resize(image, self.des_dim, interpolation=cv2.INTER_LINEAR)
        nv12_data = bgr2nv12_opencv(resized_data)
        outputs = self.models[0].forward(nv12_data)
        idx, conf = postprocess(outputs)
        label = self.classes[idx]

        return idx, conf, label

    def warmup(self):
        random_image = np.random.rand(self.des_dim[0], self.des_dim[1], 3)
        random_image_uint8 = random_image * 255
        random_image_uint8 = random_image_uint8.astype(np.uint8)
        self.infer(random_image_uint8)

    def __call__(self, image):
        t0 = time.time()
        res = self.infer(image)
        print("QRcode Resnet Cost: %.2f ms" % ((time.time() - t0) * 1000))
        return res


class QRCodeResnet(Node):
    def __init__(self):
        super().__init__("QRCodeResnet")

        self.Resnet = Resnet(_model_path)
        self.cv_bridge = CvBridge()

        # 订阅原摄像头发布的图像话题
        self.subscription_ = self.create_subscription(
            Image, "/qrcodes", self.image_callback, 1
        )
        self.subscription_ = self.create_subscription(
            Image, "/image_raw", self.image_callback, 1
        )

        # 发布二维码检测结果话题
        self.publisher_ = self.create_publisher(Int8, "qr_code_data", 1)

    def image_callback(self, msg):
        img = self.cv_bridge.imgmsg_to_cv2(msg)
        idx, conf, label = self.Resnet(img)
        res = Int8()
        res.data = int(idx) + 3
        print(idx, conf, label)

        self.publisher_.publish(res)


def main(args=None):
    rclpy.init(args=args)
    qr_code_resnet = QRCodeResnet()

    try:
        rclpy.spin(qr_code_resnet)
    except KeyboardInterrupt:
        pass

    qr_code_resnet.destroy_node()
    rclpy.shutdown()


if __name__ == "__main__":
    main()
