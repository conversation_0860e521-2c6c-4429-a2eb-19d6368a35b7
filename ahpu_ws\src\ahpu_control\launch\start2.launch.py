import os

from launch import LaunchDescription 
from launch.substitutions import LaunchConfiguration
from launch.actions import IncludeLaunchDescription, DeclareLaunchArgument
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_xml.launch_description_sources import XMLLaunchDescriptionSource
from launch_ros.actions import Node

from ament_index_python.packages import get_package_share_directory



def generate_launch_description():
    rosbridge = IncludeLaunchDescription(
        XMLLaunchDescriptionSource(
            [
                os.path.join(get_package_share_directory("rosbridge_server"), "launch"),
                "/rosbridge_websocket_launch.xml",
            ]
        )
    )


    origincar_base = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            [
                os.path.join(get_package_share_directory("origincar_base"), "launch"),
                "/origincar_bringup.launch.py",
            ]
        )
    )


    usb_cam_device_arg = DeclareLaunchArgument(
        'device',
        default_value='/dev/video8',
        description='usb camera device')
    

  
    usb_node = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(
                get_package_share_directory('hobot_usb_cam_foxy'),
                'launch/hobot_usb_cam.launch.py')),
        launch_arguments={
            'usb_image_width': '1280',
            'usb_image_height': '720',
            'usb_video_device': LaunchConfiguration('device')
        }.items()
    )




    dnn_node = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
                os.path.join(
                get_package_share_directory('dnn_node_sample_foxy'),
                'launch/yolov5.launch.py'))
    )


    qrcode = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(
                get_package_share_directory('ahpu_qrcode'),
                'launch/qrcode.launch.py'))
    )

    llm = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(
                get_package_share_directory('ahpu_llm'),
                'launch/simple_vision.launch.py'))
    )


    jpeg_codec_node = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(
                get_package_share_directory('hobot_codec'),
                'launch/hobot_codec_encode.launch.py')),
        launch_arguments={
            'codec_sub_topic': '/hbmem_img',
            'codec_in_mode': 'shared_mem',
            'codec_in_format': 'nv12',
            'codec_pub_topic': '/image',
            'codec_out_mode': 'ros',
            'codec_out_format': 'jpeg',
            'codec_jpg_quality': '5.0',
            'codec_enc_qp': '1.0',
            'codec_dump_output': 'False',
        }.items()
    )
    car_state_node = Node(
        package='ahpu_control',
        executable='car_state',
        output='screen'
    )
    control_node = Node(
        package='ahpu_control',
        executable='control_yolov5',
        output='screen'
    )
    

    return LaunchDescription(
        [
            # rosbridge桥
            rosbridge, 
            # origincar底盘
            origincar_base,
            # 摄像头及其参数 
            usb_cam_device_arg,
            usb_node, 
            # yolov5模型推理包，并且启动图像零拷贝转nv12 
            dnn_node,
            # 二维码识别 
            qrcode,
            # llm
            llm,
            # nv12加速压缩图像转jpeg给foxglove
            jpeg_codec_node,
            # 运动控制节点
            car_state_node,
            control_node
         ]
    )
