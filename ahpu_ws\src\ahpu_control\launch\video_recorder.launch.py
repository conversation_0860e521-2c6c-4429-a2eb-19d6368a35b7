from launch import LaunchDescription
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
import os


def generate_launch_description():
    usb_cam_device_arg = DeclareLaunchArgument(
        "device", default_value="/dev/video8", description="usb camera device"
    )
    usb_node = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(
                get_package_share_directory("hobot_usb_cam_foxy"),
                "launch/hobot_usb_cam.launch.py",
            )
        ),
        launch_arguments={
            "usb_image_width": "1280",
            "usb_image_height": "720",
            "usb_video_device": LaunchConfiguration("device"),
        }.items(),
    )

    video_recorder = Node(
        package="ahpu_control",
        executable="video_recorder",
        name="video_recorder",
        parameters=[
            {
                "sub_topic": "/image_compressed",
                "video_width": 1280,
                "video_height": 720,
                "video_fps": 30,
            }
        ],
    )

    ld = LaunchDescription()
    
    ld.add_action(usb_cam_device_arg)
    ld.add_action(usb_node)
    ld.add_action(video_recorder)

    return ld
