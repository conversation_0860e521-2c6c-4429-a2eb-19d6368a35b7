import os
from glob import glob
from setuptools import setup

package_name = 'ahpu_control'

setup(
    name=package_name,
    version='0.0.0',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        (os.path.join('share', package_name, 'launch'), glob(os.path.join('launch', '*launch.py')))
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='root',
    maintainer_email='<EMAIL>',
    description='TODO: Package description',
    license='TODO: License declaration',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            "control           =   ahpu_control.control:main",
            "control_yolov5    =   ahpu_control.control_yolov5:main",
            "car_state         =   ahpu_control.car_state:main",
            "begin_car         =   ahpu_control.begin_car:main",
            "video_recorder    =   ahpu_control.video_recorder:main"
        ],
    },
)
