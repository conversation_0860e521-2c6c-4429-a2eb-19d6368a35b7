import os

from ament_index_python.packages import get_package_share_directory

from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_xml.launch_description_sources import XMLLaunchDescriptionSource


def generate_launch_description():
    rosbridge = IncludeLaunchDescription(
        XMLLaunchDescriptionSource(
            [
                os.path.join(get_package_share_directory("rosbridge_server"), "launch"),
                "/rosbridge_websocket_launch.xml",
            ]
        )
    )
    usb_cam = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            [
                os.path.join(
                    get_package_share_directory("hobot_usb_cam_tros"), "launch"
                ),
                "/hobot_usb_cam.launch.py",
            ]
        )
    )
    yolov5 = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            [
                os.path.join(get_package_share_directory("ahpu_inference"), "launch"),
                "/yolov5.launch.py",
            ]
        )
    )
    control = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            [
                os.path.join(get_package_share_directory("ahpu_control"), "launch"),
                "/control.launch.py",
            ]
        )
    )

    return LaunchDescription(
        [rosbridge, usb_cam, yolov5, control]
    )
