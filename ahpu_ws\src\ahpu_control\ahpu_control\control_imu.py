import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile
from std_msgs.msg import Int32
from ai_msgs.msg import PerceptionTargets
from geometry_msgs.msg import Twist
from rclpy.logging import get_logger
from rclpy.executors import SingleThreadedExecutor, MultiThreadedExecutor
from rclpy.callback_groups import MutuallyExclusiveCallbackGroup, ReentrantCallbackGroup
from threading import Lock

import numpy as np
import time
from sensor_msgs.msg import Imu
from transforms3d.euler import quat2euler
import math


class ControlImu(Node):
    def __init__(self):
        super().__init__("control_imu")

        self.callback_group = ReentrantCallbackGroup()
        self.logger = get_logger(self.get_name())
        self.lock = Lock()  # 线程锁
        self.state = 2  # 默认是停车状态
        self.stop_state = False
        self.targets = []  # 存储yolov5的检测结果
        self.yaw = None  # 存储imu数据中的yaw角度
        self.target_yaw = None  # 存储目标方向的yaw角度
        
        self.task1 = False
        self.task2 = False

        self.declare_parameter("yolov5_topic", "/dnn_node_sample")
        self.yolov5_topic = self.get_parameter("yolov5_topic").value

        self.logger.info("IMU运动控制节点启动成功")
        if self.state == 2:
            self.logger.info("当前是停车状态")

        # 创建订阅者，订阅Yolov5的检测结果
        self.sub_yolov5 = self.create_subscription(
            PerceptionTargets,
            self.yolov5_topic,
            self.sub_yolov5_callback,
            1,
            callback_group=self.callback_group,
        )

        # 创建订阅者，订阅IMU数据
        self.sub_imu = self.create_subscription(
            Imu, "/imu/data", self.sub_imu_callback, 1, callback_group=self.callback_group
        )

        # 创建订阅者，订阅小车的状态
        self.sub_car_state = self.create_subscription(
            Int32,
            "/car_state",
            self.sub_car_state_callback,
            1,
            callback_group=self.callback_group,
        )

        # 创建定时器
        self.control_timer = self.create_timer(
            1 / 15, self.control, callback_group=self.callback_group
        )

        # 创建发布者，发布控制指令
        self.pub_cmd_vel = self.create_publisher(
            Twist, "/cmd_vel", 1, callback_group=self.callback_group
        )

    def sub_yolov5_callback(self, msg):
        # 订阅yolov5的检测结果，更新targets
        cones, lines, park, qrcode = [], [], [], []
        if len(msg.targets) > 0:
            for target in msg.targets:
                if target.type == "line":
                    lines.append(self.get_bboxes(target.rois))
                elif target.type == "cone":
                    cones.append(self.get_bboxes(target.rois))
                elif target.type == "park":
                    park.append(self.get_bboxes(target.rois))
                elif target.type == "qrcode":
                    qrcode.append(self.get_bboxes(target.rois))
            with self.lock:
                self.targets = {
                    "qrcode": qrcode,
                    "park": park,
                    "lines": lines,
                    "cones": cones,
                }
        self.logger.info(
            f"检测目标数量：{len(msg.targets)}, 线路数量：{len(lines)}, 圆锥数量：{len(cones)}, 停车区域数量：{len(park)}, 二维码数量：{len(qrcode)}"
        )
    
    def get_bboxes(self, rois):
        # 解析yolov5的检测结果，获取bbox信息
        roi = rois[0]
        x = roi.rect.x_offset
        y = roi.rect.y_offset
        w = roi.rect.width
        h = roi.rect.height

        return [x, y, w, h]

    def sub_imu_callback(self, msg):
        # 订阅imu数据，更新imu数据
        if msg.orientation_covariance[0] < 0:
            self.logger.warn("IMU数据异常，请检查IMU")
            with self.lock:
                self.yaw = None
            return

        quaternion = [
            msg.orientation.x,
            msg.orientation.y,
            msg.orientation.z,
            msg.orientation.w,
        ]

        ai, aj, ak = quat2euler(quaternion)
        yaw = math.degrees(ai)
        with self.lock:
            self.yaw = yaw

    def sub_car_state_callback(self, msg):
        # 订阅小车状态，更新状态
        with self.lock:
            self.state = msg.data
        if msg.data == 1:
            self.stop()
            self.logger.info("切换手动控制")
        elif msg.data == 0:
            self.stop()
            self.logger.info("切换自动控制")
            
            
        elif msg.data == 2:
            self.stop()
            self.logger.info("切换停车状态")
        self.stop_state = False

    def set_speed(self, speed, angular_speed):
        # 设置速度指令
        z = np.clip(z, -30.0, 30.0)
        twist_msg = Twist()

        twist_msg.linear.x = speed
        twist_msg.linear.y = 0.0
        twist_msg.linear.z = 0.0

        twist_msg.angular.x = 0.0
        twist_msg.angular.y = 0.0
        twist_msg.angular.z = float(angular_speed)

        self.pub_cmd_vel.publish(twist_msg)

    def stop(self):
        # 停止小车
        self.set_speed(0.0, 0.0)

    def control(self):
        # 控制小车运动
        with self.lock:
            state = self.state
        
        if state == 2:
            # 停车状态
            if self.target_yaw is not None:
                self.target_yaw = None
            
            if not self.stop_state:
                self.stop()
                self.stop_state = True
                
        elif state == 1:
            # 手动控制
            if not self.stop_state:
                self.stop()
                self.stop_state = True
            
        elif state == 0:
            # 自动控制
            
            self.auto_control()

    def auto_control(self):
        # 自动控制决策
        with self.lock:
            cones = self.targets["cones"]
            park = self.targets["park"]
            qrcode = self.targets["qrcode"]
            yaw = self.yaw
            tartget_yaw = self.target_yaw

        
