#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import rclpy
from rclpy.node import Node
from std_msgs.msg import String, Bool
from sensor_msgs.msg import CompressedImage
from cv_bridge import CvBridge
import cv2
import time
import threading
import numpy as np
import asyncio
import aiohttp
import json
import base64
from PIL import Image
from PIL.Image import Resampling
import io

# ==================== API密钥配置 ====================
# 请在这里设置您的API密钥
API_KEYS = {
    "BL_API_KEY": "your_baichuan_api_key_here",  # 百川API密钥
    "QF_API_KEY": "your_qianfan_api_key_here",   # 千帆API密钥
    "HS_API_KEY": "66218764-701f-4ac0-9b5d-c9de7990ee71"     # 豆包API密钥
}

# API基础URL
API_URLS = [
    "https://dashscope.aliyuncs.com/compatible-mode/v1",  # 百川
    "https://qianfan.baidubce.com/v2",                    # 千帆
    "https://ark.cn-beijing.volces.com/api/v3"            # 豆包
]

class SimpleVisionNode(Node):
    def __init__(self):
        super().__init__('simple_vision_node')
        
        # 初始化基本组件
        self.bridge = CvBridge()
        self.last_image = None
        self.isProcessingImage = False
        self.temp_image_path = '/tmp/temp_image.jpg'
        
        # 模型设置
        self.model_name = "doubao-seed-1.6-250615"
        self.api_option = 2  # 豆包API
        
        # 创建发布者
        self.result_pub = self.create_publisher(String, '/vision_text_simple', 1)
        
        # 订阅控制话题
        self.control_sub = self.create_subscription(Bool, 'llm_vision_control', self.control_callback, 10)
        
        # 只订阅首选话题
        self.create_subscription(CompressedImage, '/image_compressed', self.compressed_image_callback, 10)
        
        self.get_logger().info('大模型视觉识别节点启动成功')
    
    def compressed_image_callback(self, msg):
        """处理压缩图像消息"""
        try:
            np_arr = np.frombuffer(msg.data, np.uint8)
            self.last_image = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)
        except Exception as e:
            self.get_logger().error(f'处理压缩图像错误: {str(e)}')
    
    def control_callback(self, msg):
        """处理控制请求"""
        if not msg.data or self.isProcessingImage:
            return
            
        self.get_logger().info('开始处理图像...')
        
        if self.last_image is None:
            self.get_logger().warn('没有可用的图像')
            return
            
        self.isProcessingImage = True
        threading.Thread(target=self.process_image_thread).start()
    
    def process_image_thread(self):
        """在单独线程中处理图像"""
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 保存图像到临时文件
            cv2.imwrite(self.temp_image_path, self.last_image)
            
            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 运行异步处理函数
            result = loop.run_until_complete(self.process_image_async(self.temp_image_path))
            loop.close()
            
            # 处理并发布结果
            self._publish_result(result)
            
            # 清理
            if os.path.exists(self.temp_image_path):
                os.remove(self.temp_image_path)
            
            # 计算并打印总处理时间
            total_time = time.time() - start_time
            self.get_logger().info(f'图像处理完成，总耗时: {total_time:.3f}秒')
                
        except Exception as e:
            self.get_logger().error(f'图像处理错误: {str(e)}')
        finally:
            self.isProcessingImage = False
    
    def _publish_result(self, result):
        """处理并发布识别结果"""
        if not result:
            return
            
        try:
            result_json = json.loads(result)
            if result_json.get('status') == 'success' and 'data' in result_json:
                # 提取分析结果
                analysis_result = result_json['data']['analysis_result']
                
                # 在终端打印处理时间
                if 'processing_times' in result_json:
                    times = result_json['processing_times']
                    self.get_logger().info(f'图像压缩耗时: {times["image_processing"]:.3f}秒')
                    self.get_logger().info(f'API调用耗时: {times["api_call"]:.3f}秒')
                
                # 只发布分析结果
                msg = String()
                msg.data = analysis_result
                self.result_pub.publish(msg)
                self.get_logger().info(f'识别结果: {analysis_result}')
            else:
                error_msg = "处理失败" if result_json.get('status') == 'error' else result
                msg = String()
                msg.data = error_msg
                self.result_pub.publish(msg)
        except:
            msg = String()
            msg.data = result
            self.result_pub.publish(msg)
    
    def image_to_base64(self, image_path, max_size_mb=10):
        """压缩图像并转换为base64编码"""
        start_time = time.time()
        try:
            image = Image.open(image_path)
            if image.format == "PNG":
                image = image.convert("RGB")  # 必须转RGB，否则JPEG保存报错

            # 压缩图像
            if max(image.size) > 1024:
                image.thumbnail((1024, 1024), Resampling.LANCZOS)  # 更好的抗锯齿

            # 渐进式压缩
            quality = 90
            while True:
                buffer = io.BytesIO()
                image.save(buffer, format="JPEG", quality=quality, optimize=True)
                if buffer.tell() < max_size_mb * 1024 * 1024:
                    buffer.seek(0)
                    return base64.b64encode(buffer.getvalue()).decode('utf-8'), time.time() - start_time
                quality -= 5
                if quality < 50:
                    raise ValueError("无法将图像压缩到指定大小")
        except Exception as e:
            self.get_logger().error(f'图像处理错误: {str(e)}')
            return None, 0
    
    def get_api_key(self):
        """获取API密钥"""
        api_key_name = ["BL_API_KEY", "QF_API_KEY", "HS_API_KEY"][self.api_option]
        api_key = os.getenv(api_key_name) or API_KEYS.get(api_key_name)
        
        if not api_key or api_key == f"your_{api_key_name.lower()}_here":
            raise ValueError(f"API密钥未设置: {api_key_name}")
            
        return api_key
    
    async def process_image_async(self, image_path):
        """异步处理图像"""
        start_time = time.time()
        
        # 处理图像
        image_base64_data, image_processing_time = self.image_to_base64(image_path)
        if not image_base64_data:
            return json.dumps({"status": "error", "error": {"message": "图像处理失败"}})

        # 准备提示词
        prompt = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": [
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64_data}"}},
                {"type": "text", "text": """
                请检测图片中是否出现了带有医疗场景和人形的立牌，若没有检测到，请仅返回"没有检测到"四个字，
                如果检测到，请描述人形立牌中的医疗场景图像，注意：
                1. 人物穿着、医疗设备状态
                2. 患者表情和可能的感受
                3. 环境细节
                4. 整体氛围
                字数控制在100字以内，尽量使用简洁的语言描述。
                """}
            ]}
        ]

        try:
            # 获取API密钥和URL
            api_key = self.get_api_key()
            base_url = API_URLS[self.api_option]
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }
            payload = {
                "model": self.model_name,
                "messages": prompt
            }
            
            # 记录API调用开始时间
            api_start_time = time.time()
            
            # 发送请求
            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    f"{base_url}/chat/completions",
                    json=payload, 
                    headers=headers
                ) as response:
                    response_data = await response.json()
                    
                    # 计算API调用时间
                    api_call_time = time.time() - api_start_time
                    
                    # 简化错误处理
                    if "error" in response_data or "choices" not in response_data:
                        raise Exception(f"API响应错误: {response_data}")
                        
                    # 提取结果
                    content = response_data["choices"][0]["message"]["content"]
                    
                    # 构建结构化响应
                    result = {
                        "status": "success",
                        "data": {
                            "analysis_result": content,
                            "is_detected": "没有检测到" not in content
                        },
                        "processing_times": {
                            "image_processing": image_processing_time,
                            "api_call": api_call_time,
                            "total": time.time() - start_time
                        }
                    }

                    return json.dumps(result, ensure_ascii=False)

        except Exception as e:
            return json.dumps({
                "status": "error",
                "error": {"message": str(e)}
            }, ensure_ascii=False)

def main(args=None):
    rclpy.init(args=args)
    node = SimpleVisionNode()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main() 