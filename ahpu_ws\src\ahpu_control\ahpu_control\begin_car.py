import rclpy
from rclpy.logging import get_logger
from rclpy.node import Node

import time

from std_msgs.msg import Int32
from ahpu_interfaces.msg import Sign


class Begin(Node):
    def __init__(self, name):
        super().__init__(name)

        self.logger = get_logger(self.get_name())

        # 创建发布者，发布小车状态
        self.pub_begin = self.create_publisher(Int32, "/begin_car", 1)

        self.pub_begin_callback()

    def pub_begin_callback(self):
        while True:
            try:
                sign = int(input("输入发车指令(0: 停车, 1: 任务1, 2: 任务2, 3:任务3, 9:保完赛):"))
            except ValueError:
                continue
            except KeyboardInterrupt:
                return

            if sign == 0:
                self.pub_msg(sign)
                self.logger.info("已发送停车指令")
            elif sign == 1:
                self.pub_msg(sign)
                self.logger.info("已发送任务1指令")
            elif sign == 2:
                self.pub_msg(sign)
                self.logger.info("已发送任务2指令")
            elif sign == 3:
                self.pub_msg(sign)
                self.logger.info("已发送任务3指令")
            elif sign == 9:
                self.pub_msg(sign)
                self.logger.info("已发送任务9指令")
            else:
                self.logger.info("无效指令")

            time.sleep(0.2)

    def pub_msg(self, sign):
        msg = Int32()
        msg.data = sign
        for i in range(1):
            self.pub_begin.publish(msg)
            time.sleep(0.02)


def main(args=None):
    rclpy.init(args=args)
    begin = Begin("car_state")
    try:
        rclpy.spin(begin)
    except KeyboardInterrupt:
        pass
    begin.destroy_node()
    rclpy.shutdown()
