# 🔄 避障中的回正策略

## 📋 需求说明

用户希望实现这样的效果：
> "如果小车在避障的时候识别不到线，只能识别锥桶的时候，执行回正策略，也就是根据一开始的target的坐标进行回正"

## 🎯 实现方案

### 核心逻辑修改

```python
# PRIORITY 3: 只有锥桶的情况 - 根据避障状态决策
elif len(cones) > 0 and len(qrcode) == 0 and len(lines) == 0:
    # 检查是否在避障状态
    if avoid.avoid_state in ["AVOIDING", "RETURNING"]:
        # 🎯 关键：在避障过程中，直接执行回正策略
        target = self.execute_return_strategy()
    else:
        # 正常状态下看到锥桶，寻找二维码
        target = self.navigate_around_cones_to_find_qr(cones)
```

### 新增回正策略方法

```python
def execute_return_strategy(self):
    """执行回正策略：基于原始目标位置进行方向性回正"""
    
    # 🎯 关键：基于原始目标点进行方向性搜索
    if avoid.original_target is not None:
        original_x = avoid.original_target[0]
        
        if original_x < 500:  # 原来在左侧
            search_target = np.array([450, 320])  # 向左回正
        elif original_x > 660:  # 原来在右侧
            search_target = np.array([710, 320])  # 向右回正
        else:  # 原来在中间
            search_target = np.array([580, 320])  # 中心回正
    else:
        # 没有原始目标点，使用默认中心搜索
        search_target = np.array([580, 320])
    
    return search_target
```

## 🔄 工作流程

### 场景演示

```python
# 🎬 完整流程演示

# t=0: 正常跟随地标线
detection = {"lines": [[500, 300, 100, 20]], "cones": [], "qrcode": []}
target = [500, 300]  # 地标线位置
avoid.avoid_state = "NORMAL"

# t=1: 检测到锥桶，开始避障
detection = {"lines": [[500, 300, 100, 20]], "cones": [[480, 320, 40, 60]], "qrcode": []}
avoid.avoid_state = "AVOIDING"
avoid.original_target = [500, 300]  # 🔑 保存原始目标
target = [440, 300]  # 避障目标

# t=2: 避障中，失去地标线，只能看到锥桶
detection = {"lines": [], "cones": [[480, 320, 40, 60]], "qrcode": []}
# 🎯 触发新策略：只有锥桶 + 避障状态 = 执行回正策略
target = execute_return_strategy()  # 基于 original_target[0]=500
# 因为 500 在 [500, 660] 范围内，所以 target = [580, 320] (中心回正)

# t=3: 继续回正
detection = {"lines": [], "cones": [], "qrcode": []}
# 继续向中心回正，寻找地标线

# t=4: 找到地标线，回正完成
detection = {"lines": [[520, 310, 100, 20]], "cones": [], "qrcode": []}
avoid.clear()  # 回正完成
avoid.avoid_state = "NORMAL"
target = [520, 310]  # 跟随新的地标线
```

## 📊 策略对比

| 情况 | 原策略 | 新策略 | 优势 |
|------|--------|--------|------|
| **避障中只见锥桶** | 搜索二维码 | 执行回正 | ✅ 更快回到路径 |
| **正常状态见锥桶** | 搜索二维码 | 搜索二维码 | ✅ 保持原逻辑 |
| **回正方向** | 基于锥桶位置 | 基于原始目标 | ✅ 更合理的方向 |

## 🎯 关键改进

### 1. 状态感知决策
```python
# 根据避障状态选择不同策略
if avoid.avoid_state in ["AVOIDING", "RETURNING"]:
    # 避障中：优先回正
    target = self.execute_return_strategy()
else:
    # 正常状态：寻找二维码
    target = self.navigate_around_cones_to_find_qr(cones)
```

### 2. 基于历史信息的回正
```python
# 使用保存的原始目标位置
if avoid.original_target is not None:
    original_x = avoid.original_target[0]
    # 根据原始位置决定回正方向
```

### 3. 详细的日志记录
```python
self.logger.info(f"RETURN: 基于原始目标({avoid.original_target})，向左回正到{search_target}")
```

## 🚀 实际效果

### 避障回正更快
- ❌ 原来：避障中只见锥桶 → 搜索二维码 → 可能偏离更远
- ✅ 现在：避障中只见锥桶 → 直接回正 → 快速回到路径

### 方向更合理
- ❌ 原来：基于锥桶平均位置决定方向
- ✅ 现在：基于避障前的目标位置决定方向

### 逻辑更清晰
- ✅ 避障状态：优先回正找路径
- ✅ 正常状态：寻找二维码目标

## 💡 使用建议

### 参数调优
```python
# 回正区域可以根据实际情况调整
if original_x < 500:     # 左侧阈值
    search_target = [450, 320]  # 左侧回正点
elif original_x > 660:   # 右侧阈值  
    search_target = [710, 320]  # 右侧回正点
```

### 超时保护
```python
# 回正超时时间可以调整
if avoid.return_step > 10:  # 约3秒
    # 强制重置，避免无限回正
```

## 🎯 总结

这个新的回正策略实现了用户的需求：
- ✅ 避障中只见锥桶时，直接执行回正
- ✅ 基于原始目标位置进行方向性回正
- ✅ 保持了原有的二维码搜索逻辑
- ✅ 提高了回正效率和准确性

现在小车在避障过程中遇到只有锥桶的情况时，会智能地根据避障前的目标位置进行回正，更快地回到正确的行驶路径！🚗💨
