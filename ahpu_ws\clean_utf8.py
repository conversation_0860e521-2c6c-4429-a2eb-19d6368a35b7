#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Clean UTF-8 encoding issues by removing or replacing problematic characters
"""

import re

def clean_utf8_file():
    file_path = "src/ahpu_control/ahpu_control/control_yolov5.py"
    
    # Read file with error handling
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        # If UTF-8 fails, read with error replacement
        with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()
    
    # Replace problematic characters
    # Remove or replace Unicode replacement characters
    content = content.replace('\ufffd', '')  # Remove replacement characters
    
    # Replace common Chinese characters with English equivalents
    chinese_to_english = {
        '运动控制节点启动成功': 'Motion control node started successfully',
        '当前是停车状态': 'Currently in parking state',
        '检测目标数量': 'Detected targets',
        '线路数量': 'Lines',
        '圆锥数量': 'Cones',
        '停车区域数量': 'Parking areas',
        '二维码数量': 'QR codes',
        '进入任务1': 'Entering task 1',
        '进入任务2': 'Entering task 2',
        '进入任务3': 'Entering task 3',
        '进入模型图生文': 'Entering model image-to-text',
        '停车重置': 'Parking reset',
        '保完赛': 'Race completed',
        '检测到二维码，前往二维码': 'QR code detected, moving to QR code',
        '二维码距离很近，停车识别': 'QR code very close, stop for recognition',
        '未找到二维码，前往地标线': 'QR code not found, moving to landmark line',
        '目标点坐标': 'Target coordinates',
        '速度': 'Speed',
        '角度': 'Angle',
        '没有轨迹线': 'No trajectory lines',
        '只有一个轨迹线，无法拟合，直接返回': 'Only one trajectory line, cannot fit, return directly',
        '有两个轨迹线，返回最远的那个': 'Two trajectory lines, return the farthest one',
        '有多个轨迹线，曲线拟合': 'Multiple trajectory lines, curve fitting',
        '线程锁': 'Thread lock',
        '任务1策略代码': 'Task 1 strategy code',
        '优化版：智能目标搜索': 'Optimized: intelligent target search',
        '智能避障算法': 'Intelligent obstacle avoidance algorithm',
        '二维码检测（最高优先级）': 'QR code detection (highest priority)',
        '地标线跟踪': 'Landmark line tracking',
        '只检测到锥桶，无地标线和二维码': 'Only cones detected, no landmark lines or QR codes',
        '未检测到任何目标，启动盲飞导航': 'No targets detected, starting blind navigation',
        '盲飞控制': 'Blind navigation control',
        '智能避障': 'Intelligent obstacle avoidance',
        '障碍物位置': 'Obstacle position',
        '原目标': 'Original target',
        '新目标': 'New target',
    }
    
    # Apply replacements
    for chinese, english in chinese_to_english.items():
        content = content.replace(chinese, english)
    
    # Remove any remaining Chinese characters (replace with safe ASCII)
    content = re.sub(r'[\u4e00-\u9fff]+', '[Chinese removed]', content)
    
    # Remove other problematic Unicode characters
    content = re.sub(r'[^\x00-\x7F]+', '[Non-ASCII removed]', content)
    
    # Clean up multiple spaces but preserve newlines
    content = re.sub(r'[ \t]+', ' ', content)  # Replace multiple spaces/tabs with single space
    content = re.sub(r' +\n', '\n', content)  # Remove trailing spaces before newlines
    content = re.sub(r'\n\n+', '\n\n', content)  # Replace multiple newlines with double newlines
    
    # Write back with UTF-8 encoding
    with open(file_path, 'w', encoding='utf-8', newline='\n') as f:
        f.write(content)
    
    print("UTF-8 cleaning completed!")
    print("All problematic characters have been removed or replaced.")

if __name__ == "__main__":
    clean_utf8_file()
