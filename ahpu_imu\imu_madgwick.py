#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Imu
from geometry_msgs.msg import Vector3
from std_msgs.msg import Float64, Float32, Bool
import numpy as np
import time
import math
from ahrs.filters import Madgwick

class IMUProcessorMadgwick(Node):
    """
    IMU处理节点，使用Madgwick滤波器处理原始IMU数据
    提供稳定的航向角(yaw)、俯仰角(pitch)和翻滚角(roll)
    """
    def __init__(self):
        super().__init__('imu_processor_madgwick')
        
        # 声明核心参数
        self.declare_parameter('sample_frequency', 100.0)  # IMU采样频率
        self.declare_parameter('gain', 0.1)  # Madgwick滤波器增益
        self.declare_parameter('calibration_time', 2.0)  # 校准时间(秒)
        self.declare_parameter('large_angle_compensation', 1.12)  # 大角度补偿因子
        self.declare_parameter('gyro_threshold', 0.001)  # 陀螺仪积分阈值
        self.declare_parameter('auto_zero', True)  # 是否启用长时间静止自动归零
        self.declare_parameter('stationary_time_threshold', 3.0)  # 静止时间阈值(秒)
        self.declare_parameter('heading_output_interval', 0.5)  # 航向角输出间隔(秒)
        self.declare_parameter('significant_change', 1.0)  # 有意义的角度变化阈值(度)
        
        # 获取参数
        self.sample_frequency = self.get_parameter('sample_frequency').value
        self.gain = self.get_parameter('gain').value
        self.calibration_time = self.get_parameter('calibration_time').value
        self.large_angle_compensation = self.get_parameter('large_angle_compensation').value
        self.gyro_threshold = self.get_parameter('gyro_threshold').value
        self.auto_zero = self.get_parameter('auto_zero').value
        self.stationary_time_threshold = self.get_parameter('stationary_time_threshold').value
        self.heading_output_interval = self.get_parameter('heading_output_interval').value
        self.significant_change = self.get_parameter('significant_change').value
        
        # 创建Madgwick滤波器
        self.filter = Madgwick(frequency=self.sample_frequency, gain=self.gain)
        self.q = np.array([1.0, 0.0, 0.0, 0.0])  # 初始四元数 [w, x, y, z]
        
        # 初始化变量
        self.is_calibrated = False
        self.calibration_start_time = None
        self.gyro_bias = np.zeros(3)
        self.calibration_samples = 0
        
        # 角度变量
        self.yaw = 0.0
        self.pitch = 0.0
        self.roll = 0.0
        self.last_yaw = 0.0  # 用于计算航向角变化
        
        # 陀螺仪积分变量
        self.last_time = None
        self.gyro_integrated_angle = 0.0
        self.last_output_time = 0.0  # 上次输出航向角的时间
        self.last_output_angle = 0.0  # 上次输出的航向角
        
        # 静止检测和锁定变量
        self.stationary_counter = 0
        self.stationary_start_time = None
        self.stationary_lock = False
        self.locked_yaw = 0.0
        
        # 历史数据用于平滑
        self.gyro_history = []
        self.accel_history = []
        self.history_size = 5
        
        # 创建订阅者
        self.imu_sub = self.create_subscription(
            Imu,
            '/imu/data_raw',
            self.imu_callback,
            10
        )
        
        # 创建重置角度的订阅者
        self.reset_sub = self.create_subscription(
            Bool,
            '/reset_heading',
            self.reset_heading_callback,
            10
        )
        
        # 创建发布者
        self.euler_pub = self.create_publisher(Vector3, '/imu/euler', 10)
        self.stabilized_heading_pub = self.create_publisher(Float32, '/stabilized_heading', 10)
        
        # 创建定时器，定期发布稳定的航向角
        self.timer = self.create_timer(0.1, self.publish_stabilized_heading)
        
        self.get_logger().info('IMU Madgwick处理器已启动')
        self.get_logger().info(f'采样频率: {self.sample_frequency} Hz, 增益: {self.gain}')
        self.get_logger().info(f'校准时间: {self.calibration_time} 秒')
        self.get_logger().info(f'大角度补偿因子: {self.large_angle_compensation}')
        self.get_logger().info(f'陀螺仪积分阈值: {self.gyro_threshold}')
        self.get_logger().info(f'长时间静止自动归零: {self.auto_zero}')
        self.get_logger().info(f'静止时间阈值: {self.stationary_time_threshold} 秒')
        self.get_logger().info(f'航向角输出间隔: {self.heading_output_interval} 秒')
        self.get_logger().info(f'有意义的角度变化阈值: {self.significant_change} 度')
        self.get_logger().info('发布 /reset_heading 话题(Bool类型)可重置航向角')
    
    def reset_heading_callback(self, msg):
        """重置航向角和累积角度"""
        if msg.data:
            self.get_logger().info('收到重置航向角请求')
            self.gyro_integrated_angle = 0.0
            self.last_output_angle = 0.0
            self.get_logger().info('航向角已重置')
    
    def detect_stationary(self, msg):
        """严格的静止检测"""
        angular_velocity = msg.angular_velocity
        linear_acceleration = msg.linear_acceleration
        
        # 计算角速度平方和
        angular_velocity_sq_sum = (
            angular_velocity.x ** 2 + 
            angular_velocity.y ** 2 + 
            angular_velocity.z ** 2
        )
        
        # 计算加速度与重力的差值
        acc_magnitude = math.sqrt(
            linear_acceleration.x ** 2 + 
            linear_acceleration.y ** 2 + 
            linear_acceleration.z ** 2
        )
        gravity_diff = abs(acc_magnitude - 9.8)
        
        # 静止判断条件
        return angular_velocity_sq_sum < 0.00005 and gravity_diff < 0.3
    
    def imu_callback(self, msg):
        """处理接收到的IMU消息"""
        # 获取当前时间
        current_time = time.time()
        
        # 检测是否静止
        is_stationary = self.detect_stationary(msg)
        
        # 更新静止计数器和时间
        if is_stationary:
            self.stationary_counter += 1
            
            # 记录开始静止的时间
            if self.stationary_counter == 1:
                self.stationary_start_time = current_time
                
            # 计算静止持续时间
            stationary_duration = 0
            if self.stationary_start_time is not None:
                stationary_duration = current_time - self.stationary_start_time
            
            # 长时间静止自动归零
            if self.auto_zero and stationary_duration > self.stationary_time_threshold and not self.stationary_lock:
                if abs(self.gyro_integrated_angle) > 0.5:  # 如果当前角度不为零
                    self.get_logger().info(f'检测到长时间静止({stationary_duration:.1f}秒)，自动归零，原角度: {self.gyro_integrated_angle:.2f}°')
                    self.gyro_integrated_angle = 0.0
            
            # 锁定航向角
            if self.stationary_counter > 30 and not self.stationary_lock:  # 3秒后锁定
                self.stationary_lock = True
                if self.is_calibrated:
                    self.locked_yaw = self.yaw
                    self.get_logger().info(f"检测到静止状态，锁定航向角: {self.locked_yaw:.2f} 度")
        else:
            if self.stationary_counter > 30:  # 如果之前已经静止一段时间
                self.get_logger().info("检测到运动，解除航向角锁定")
                # 保持航向角连续，防止跳变
                self.gyro_integrated_angle = self.locked_yaw
            self.stationary_counter = 0
            self.stationary_start_time = None
            self.stationary_lock = False
        
        # 提取IMU数据
        accel = np.array([
            msg.linear_acceleration.x,
            msg.linear_acceleration.y,
            msg.linear_acceleration.z
        ])
        
        gyro = np.array([
            msg.angular_velocity.x,
            msg.angular_velocity.y,
            msg.angular_velocity.z
        ])
        
        # 执行初始校准
        if not self.is_calibrated:
            self.perform_calibration(gyro)
            self.last_time = current_time
            return
        
        # 如果处于锁定状态，直接使用锁定的航向角
        if self.stationary_lock:
            # 仍然发布当前锁定的航向角
            self.publish_current_orientation(msg.header)
            return
        
        # 应用陀螺仪零偏校准
        gyro_calibrated = gyro - self.gyro_bias
        
        # 添加到历史数据
        self.gyro_history.append(gyro_calibrated)
        self.accel_history.append(accel)
        
        # 保持历史数据窗口大小
        if len(self.gyro_history) > self.history_size:
            self.gyro_history.pop(0)
            self.accel_history.pop(0)
        
        # 应用中值滤波
        if len(self.gyro_history) == self.history_size:
            gyro_filtered = np.median(self.gyro_history, axis=0)
            accel_filtered = np.median(self.accel_history, axis=0)
        else:
            gyro_filtered = gyro_calibrated
            accel_filtered = accel
        
        # 记录上一次的航向角
        previous_integrated_angle = self.gyro_integrated_angle
        
        # 陀螺仪积分计算累积角度
        if self.last_time is not None:
            dt = current_time - self.last_time
            # 使用z轴角速度（绕垂直轴旋转）
            z_angular_velocity = gyro_filtered[2]
            # 如果角速度足够大，才累积角度，避免漂移
            if abs(z_angular_velocity) > self.gyro_threshold:
                # 将角速度从弧度/秒转换为度/秒，然后乘以时间间隔得到角度变化
                angle_change = math.degrees(z_angular_velocity) * dt
                
                # 应用大角度补偿
                if abs(self.gyro_integrated_angle) > 80.0 and abs(self.gyro_integrated_angle) < 100.0:
                    angle_change *= self.large_angle_compensation
                
                self.gyro_integrated_angle += angle_change
                
                # 计算航向角变化
                heading_change = self.gyro_integrated_angle - previous_integrated_angle
                
                # 如果角度变化超过阈值或者距离上次输出时间已经超过间隔，则输出航向角
                if (abs(self.gyro_integrated_angle - self.last_output_angle) >= self.significant_change or 
                    current_time - self.last_output_time >= self.heading_output_interval):
                    self.get_logger().info(f"航向角: {self.gyro_integrated_angle:.2f}° (变化: {heading_change:.2f}°, 角速度: {math.degrees(z_angular_velocity):.2f}°/s)")
                    self.last_output_angle = self.gyro_integrated_angle
                    self.last_output_time = current_time
        
        self.last_time = current_time
        
        # 归一化加速度数据
        if np.linalg.norm(accel_filtered) != 0:
            accel_normalized = accel_filtered / np.linalg.norm(accel_filtered)
        else:
            accel_normalized = np.array([0.0, 0.0, 1.0])  # 默认重力方向
        
        # 更新Madgwick滤波器
        self.filter.gain = self.gain
        self.q = self.filter.updateIMU(
            q=self.q,
            gyr=np.radians(gyro_filtered),  # 转换为弧度
            acc=accel_normalized
        )
        
        # 从四元数中获取欧拉角
        self.roll, self.pitch, self.yaw = self.quaternion_to_euler(self.q)
        
        # 发布当前姿态
        self.publish_current_orientation(msg.header)
    
    def publish_current_orientation(self, header):
        """发布当前姿态信息"""
        try:
            # 发布欧拉角
            euler_msg = Vector3()
            euler_msg.x = float(self.roll)
            euler_msg.y = float(self.pitch)
            euler_msg.z = float(self.yaw)
            self.euler_pub.publish(euler_msg)
        except Exception as e:
            self.get_logger().error(f'发布姿态信息时出错: {str(e)}')
    
    def publish_stabilized_heading(self):
        """定期发布稳定的航向角"""
        if not self.is_calibrated:
            return
        
        msg = Float32()
        
        # 如果处于锁定状态，使用锁定的航向角
        if self.stationary_lock:
            output_angle = self.locked_yaw
        else:
            # 使用陀螺仪积分计算的角度
            output_angle = self.gyro_integrated_angle
        
        # 应用大角度补偿
        if abs(output_angle) > 80.0 and abs(output_angle) < 100.0:
            output_angle *= self.large_angle_compensation
        
        msg.data = float(output_angle)
        self.stabilized_heading_pub.publish(msg)
        self.get_logger().info(f'航向角: {output_angle:.2f}°')
    
    def perform_calibration(self, gyro):
        """执行陀螺仪零偏校准"""
        if self.calibration_start_time is None:
            self.calibration_start_time = time.time()
            self.get_logger().info('开始IMU校准...')
            return
        
        # 累积陀螺仪读数用于校准
        self.gyro_bias += gyro
        self.calibration_samples += 1
        
        # 检查校准是否完成
        if time.time() - self.calibration_start_time >= self.calibration_time:
            # 计算平均零偏
            if self.calibration_samples > 0:
                self.gyro_bias /= self.calibration_samples
                self.get_logger().info(f'校准完成. 陀螺仪零偏: [{self.gyro_bias[0]:.6f}, {self.gyro_bias[1]:.6f}, {self.gyro_bias[2]:.6f}]')
                self.is_calibrated = True
                
                # 重置累积角度
                self.gyro_integrated_angle = 0.0
                self.last_output_angle = 0.0
                self.last_output_time = time.time()
            else:
                self.get_logger().error('校准失败: 没有收集到样本')
                self.calibration_start_time = None  # 重新开始校准
    
    def quaternion_to_euler(self, q):
        """
        将四元数转换为欧拉角(roll, pitch, yaw)
        使用航空航天序列 (z-y'-x'')
        返回角度值(度)
        """
        # 提取四元数分量
        w, x, y, z = q
        
        # 计算欧拉角
        # 翻滚角 (x轴)
        sinr_cosp = 2.0 * (w * x + y * z)
        cosr_cosp = 1.0 - 2.0 * (x * x + y * y)
        roll = math.atan2(sinr_cosp, cosr_cosp)
        
        # 俯仰角 (y轴)
        sinp = 2.0 * (w * y - z * x)
        if abs(sinp) >= 1:
            pitch = math.copysign(math.pi / 2, sinp)  # 使用90度，如果越界
        else:
            pitch = math.asin(sinp)
        
        # 航向角 (z轴)
        siny_cosp = 2.0 * (w * z + x * y)
        cosy_cosp = 1.0 - 2.0 * (y * y + z * z)
        yaw = math.atan2(siny_cosp, cosy_cosp)
        
        # 转换为度
        return math.degrees(roll), math.degrees(pitch), math.degrees(yaw)

def main(args=None):
    rclpy.init(args=args)
    node = IMUProcessorMadgwick()
    rclpy.spin(node)
    node.destroy_node()
    rclpy.shutdown()

if __name__ == '__main__':
    main() 