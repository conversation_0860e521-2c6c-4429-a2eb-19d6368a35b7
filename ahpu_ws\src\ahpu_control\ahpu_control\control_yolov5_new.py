import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile
from std_msgs.msg import Int32, Float32, String
from ai_msgs.msg import PerceptionTargets
from geometry_msgs.msg import Twist
from rclpy.logging import get_logger
from rclpy.executors import MultiThreadedExecutor
from rclpy.callback_groups import ReentrantCallbackGroup
from threading import Lock
from scipy.optimize import curve_fit

import numpy as np
import time

#
# class Avoid:
#     def __init__(self):
#         self.flag = None      # 规避标志
#         self.direction = None # 规避方向
#         self.pixel = None     # 规避像素
#         self.duration = None  # 规避时长
#
#     def __call__(self):
#         return self.flag, self.direction, self.pixel, self.duration
#
#     def clear(self):
#         self.flag = None
#         self.direction = None
#         self.pixel = None
#         self.duration = None
#
#
# avoid = Avoid()

class Curve_fitter:
    def __init__(self):
        pass

    def __call__(self, lines_center: np.ndarray, y: int) -> np.ndarray:
        if len(lines_center) == 0:  # 没有轨迹线
            return np.array([740, y])
        elif len(lines_center) == 1:  # 只有一个轨迹线，无法拟合
            return lines_center
        elif len(lines_center) == 2:  # 有两个轨迹线，返回最远的那个
            line_max = lines_center[np.argmin(lines_center[:, 1])]
            return line_max
        else:
            x_data = lines_center[:, 0]
            y_data = lines_center[:, 1]
            popt, _ = curve_fit(self.cruve_model_quadratic, y_data, x_data, p0=[1,1,1])
            target_x = self.cruve_model_quadratic(y, *popt)
            return np.array([target_x, y])

    @staticmethod
    def cruve_model_quadratic(x, a, b, c):
        return a * x**2 + b * x + c

fitter = Curve_fitter()


class Control(Node):
    def __init__(self, name):
        super().__init__(name)
        self.callback_group = ReentrantCallbackGroup()
        self.logger = get_logger(self.get_name())
        self.lock = Lock()
        self.state = 0
        self.stop_state = False
        self.targets = {}
        self.reset_targets()
        self.arrive_park = 0
        self.arrive_qrcode = 0

        # 记录任务1开始时间，用于左侧线过滤时序
        self.start_time = time.time()

        # 二维码识别相关变量
        self.qrcode_recognition_start_time = None
        self.qrcode_recognition_duration = 5.0
        self.qrcode_detection_timeout = 3.0
        self.qrcode_result = None
        self.qrcode_result_time = None
        self.qrcode_retry_count = 0
        self.qrcode_max_retries = 3

        # 日志控制
        self.last_log_time = {}
        self.log_interval = 0.5
        self.verbose_logging = False

        # 航向角相关
        self.current_yaw = 0.0
        self.target_yaw = 20.0
        self.yaw_tolerance = 5.0

        # 避障相关
        self.avoidance_severity = 0.0

        # 通信延迟监测
        self.last_detection_time = time.time()
        self.max_detection_delay = 0.5

        # 声明参数
        self.declare_parameter("yolov5_topic", "/dnn_node_sample")
        self.declare_parameter("target_yaw", 20.0)
        self.declare_parameter("yaw_tolerance", 5.0)
        self.declare_parameter("verbose_logging", False)
        self.declare_parameter("log_interval", 0.5)
        self.declare_parameter("qrcode_recognition_duration", 3.0)
        self.declare_parameter("qrcode_detection_timeout", 2.0)
        self.declare_parameter("qrcode_result_timeout", 1.0)
        # 新增左侧线过滤时长参数
        self.declare_parameter("line_filter_duration", 4.0)

        # 获取参数
        self.yolov5_topic = self.get_parameter("yolov5_topic").value
        self.target_yaw = self.get_parameter("target_yaw").value
        self.yaw_tolerance = self.get_parameter("yaw_tolerance").value
        self.verbose_logging = self.get_parameter("verbose_logging").value
        self.log_interval = self.get_parameter("log_interval").value
        self.qrcode_recognition_duration = self.get_parameter("qrcode_recognition_duration").value
        self.qrcode_detection_timeout = self.get_parameter("qrcode_detection_timeout").value
        self.qrcode_result_timeout = self.get_parameter("qrcode_result_timeout").value
        self.line_filter_duration = self.get_parameter("line_filter_duration").value

        self.logger.info("运动控制节点启动成功")
        self.logger.info(f"左侧线过滤时长: {self.line_filter_duration}s")
        self.logger.info(f"二维码识别参数: 持续{self.qrcode_recognition_duration}s, 超时{self.qrcode_detection_timeout}s, 结果超时{self.qrcode_result_timeout}s")

        # 订阅和发布
        self.sub_yolov5 = self.create_subscription(
            PerceptionTargets, self.yolov5_topic, self.sub_yolov5_callback, 5,
            callback_group=self.callback_group)
        self.sub_heading = self.create_subscription(
            Float32, '/stabilized_heading', self.sub_heading_callback, 1,
            callback_group=self.callback_group)
        self.sub_qrcode_result = self.create_subscription(
            Int32, '/qr_data', self.sub_qrcode_result_callback, 1,
            callback_group=self.callback_group)
        self.sub_qrcode_raw = self.create_subscription(
            String, '/qr_raw_string', self.sub_qrcode_raw_callback, 1,
            callback_group=self.callback_group)
        self.pub_cmd_vel = self.create_publisher(
            Twist, "/cmd_vel", 1, callback_group=self.callback_group)
        self.sub_car_state = self.create_subscription(
            Int32, "/car_state", self.sub_car_state_callback, 1,
            callback_group=self.callback_group)
        self.control_timer = self.create_timer(1/20, self.control,
                                              callback_group=self.callback_group)

    def should_log(self, key):
        now = time.time()
        if key not in self.last_log_time or now - self.last_log_time[key] > self.log_interval:
            self.last_log_time[key] = now
            return True
        return False

    ################################################ 以下函数要去除，或者修改
    def is_qrcode_recognition_active(self):
        if self.qrcode_recognition_start_time is None:
            return False
        return time.time() - self.qrcode_recognition_start_time < self.qrcode_recognition_duration

    def start_qrcode_recognition(self):
        self.qrcode_recognition_start_time = time.time()
        self.qrcode_result = None
        self.qrcode_result_time = None
        self.qrcode_retry_count = 0  # 新任务开始时重置重试计数
        if self.should_log("qrcode_recognition_start"):
            self.logger.info("开始二维码识别")

    def stop_qrcode_recognition(self):
        self.qrcode_recognition_start_time = None
        self.qrcode_result = None
        self.qrcode_result_time = None
        if self.should_log("qrcode_recognition_stop"):
            self.logger.info("停止二维码识别")

    def sub_qrcode_result_callback(self, msg):
        try:
            self.qrcode_result = int(msg.data)
            self.qrcode_result_time = time.time()
            if self.should_log("qrcode_result_received"):
                self.logger.info(f"收到二维码识别结果: {self.qrcode_result}")
        except Exception as e:
            if self.should_log("qrcode_result_error"):
                self.logger.error(f"二维码识别回调出错: {e}")

    def sub_qrcode_raw_callback(self, msg):
        try:
            content = msg.data.strip()
            if content and content != "未识别到二维码内容":
                if self.should_log("qrcode_raw_received"):
                    self.logger.info(f"收到二维码原始内容: {content}")
        except Exception as e:
            if self.should_log("qrcode_raw_error"):
                self.logger.error(f"二维码原始内容回调出错: {e}")
    ##############################################################

    def sub_yolov5_callback(self, msg):
        self.last_detection_time = time.time()
        cones, lines, park, qrcode = [], [], [], []
        for target in msg.targets:
            if target.type == "line":
                lines.append(self.get_bboxes(target.rois))
            elif target.type == "cone":
                cones.append(self.get_bboxes(target.rois))
            elif target.type == "park":
                park.append(self.get_bboxes(target.rois))
            elif target.type == "qrcode":
                qrcode.append(self.get_bboxes(target.rois))
        with self.lock:
            self.targets = {"qrcode": qrcode, "park": park, "lines": lines, "cones": cones}

        # 检测延迟监测
        if time.time() - self.last_detection_time > self.max_detection_delay and self.should_log("detection_delay"):
            self.logger.warning(f"检测延迟: {time.time()-self.last_detection_time:.2f}s")

        if len(msg.targets) > 0 and self.should_log("detection_summary"):
            self.logger.info(f"检测到: 线路{len(lines)}个, 圆锥{len(cones)}个, 停车{len(park)}个, 二维码{len(qrcode)}个")

    def sub_heading_callback(self, msg):
        try:
            with self.lock:
                self.current_yaw = float(msg.data)
        except Exception as e:
            if self.should_log("heading_error"):
                self.logger.error(f"航向角回调出错: {e}")

    def sub_car_state_callback(self, msg):
        with self.lock:
            self.state = msg.data
        if msg.data == 1:
            self.logger.info("进入任务1，重置左侧线过滤计时")
            self.arrive_qrcode = 0
            self.arrive_park = 0
            self.stop_qrcode_recognition()
            # 重置任务1开始时间
            self.start_time = time.time()
        elif msg.data == 2:
            self.logger.info("进入任务2")
            self.arrive_qrcode = 999
            self.arrive_park = 0
            self.stop_qrcode_recognition()
        elif msg.data == 3:
            self.logger.info("进入任务3")
            self.arrive_qrcode = 999
            self.arrive_park = 0
            self.stop_qrcode_recognition()
        elif msg.data == 0:
            self.logger.info("停车重置")
            self.arrive_qrcode = 0
            self.arrive_park = 0
            self.stop_qrcode_recognition()
        elif msg.data == 9:
            self.logger.info("任务结束停车")
            self.arrive_qrcode = 999
            self.arrive_park = 999
            self.stop_qrcode_recognition()
        self.stop()
        with self.lock:
            self.stop_state = False

    def control(self):
        with self.lock:
            state = self.state

        if state == 0:  # 停车状态
            if not self.stop_state:
                self.stop()
                with self.lock:
                    self.stop_state = True

        elif state == 1:  # 任务1
            with self.lock:
                targets = self.targets.copy()
            self.reset_targets()
            self.action_task1_new(targets)

        elif state == 2:  # 任务2（遥操作，无自动控制）
            if not self.stop_state:
                self.stop()
                with self.lock:
                    self.stop_state = True

        elif state == 3:  # 任务3
            with self.lock:
                targets = self.targets.copy()
            self.reset_targets()
            self.action_task2(targets)

        elif state == 9:  # 完成任务，后退停车
            self.set_speed(0.4, 0.0)
            time.sleep(1.5)
            self.set_speed(-0.4, 0.0)
            time.sleep(1.4)
            self.stop()
            with self.lock:
                self.state = 0
            self.stop()
            time.sleep(0.1)

    def action_task1_new(self, targets):
        """任务1策略：巡线为主，辅助避障，二维码识别为先决条件"""
        qrcode = np.array(targets["qrcode"]).reshape(-1, 4)
        cones  = np.array(targets["cones"]).reshape(-1, 4)
        lines = np.array(targets["lines"]).reshape(-1, 4)
        current_time = time.time()

        current_time = time.time()
        # 二维码识别
        if len(qrcode) > 0:
            bottom_centers = np.concatenate(
                (qrcode[:,0:1] + qrcode[:,2:3]/2, qrcode[:,1:2] + qrcode[:,3:4]), axis=1)
            far_qrcode = bottom_centers[np.argmin(bottom_centers[:,1])]  # 最远二维码
            if far_qrcode[1] > 210:
                if not self.is_qrcode_recognition_active():
                    self.start_qrcode_recognition()
                if self.should_log("qrcode_close"):
                    self.logger.info(f"二维码接近(y={far_qrcode[1]:.1f})，停车识别")
                self.set_speed(0.0, 0.0)
                # 有结果则处理
                if self.qrcode_result is not None and self.qrcode_result_time is not None:
                    if current_time - self.qrcode_result_time < self.qrcode_result_timeout:
                        if self.should_log("qrcode_success"):
                            self.logger.info(f"二维码识别成功，结果: {self.qrcode_result}")
                        self.arrive_qrcode += 1
                        self.stop_qrcode_recognition()
                        return
                    else:
                        if self.should_log("qrcode_expired"):
                            self.logger.warning("二维码结果过期，重置")
                        self.qrcode_result = None
                        self.qrcode_result_time = None
                # 检查识别超时
                if self.is_qrcode_recognition_active():
                    if current_time - self.qrcode_recognition_start_time > self.qrcode_detection_timeout:
                        self.qrcode_retry_count += 1
                        if self.qrcode_retry_count > self.qrcode_max_retries:
                            if self.should_log("qrcode_fail"):
                                self.logger.error("二维码多次识别失败，进入下一任务")
                            with self.lock:
                                self.state = 2
                            return
                        if self.should_log("qrcode_timeout"):
                            self.logger.warning("二维码识别超时，重新尝试")
                        self.start_qrcode_recognition()
                    return
                else:
                    if self.should_log("qrcode_retry"):
                        self.logger.info("二维码识别时间到，重新开始")
                    self.start_qrcode_recognition()
                    return
            else:
                # 二维码距离远，停止识别
                if self.is_qrcode_recognition_active():
                    self.stop_qrcode_recognition()
                if self.should_log("qrcode_far"):
                    self.logger.info(f"二维码较远(y={far_qrcode[1]:.1f})，继续巡线")
        ######### else逻辑有问题，要修改
        else:
            if self.is_qrcode_recognition_active():
                if time.time() - self.qrcode_recognition_start_time > self.qrcode_detection_timeout:
                    if self.should_log("qrcode_timeout2"):
                        self.logger.warning("二维码识别超时，无标记")
                    self.stop_qrcode_recognition()
                else:
                    return  # 等待识别

        # 避障控制
        if len(qrcode) > 0:
            bottom_centers_qr = np.concatenate(
                (qrcode[:, 0:1] + qrcode[:, 2:3] / 2, qrcode[:, 1:2] + qrcode[:, 3:4]), axis=1)
            max_dist_qrcode = bottom_centers_qr[np.argmin(bottom_centers_qr[:, 1])]  # 最远二维码
            
            # 只有当二维码非常接近时才停车识别，否则正常巡线
            if max_dist_qrcode[1] > 190:  # 以二维码为优先级
                if not self.is_qrcode_recognition_active():
                    self.start_qrcode_recognition()
                if self.should_log("qrcode_close"):
                    self.logger.info(f"二维码接近(y={max_dist_qrcode[1]:.1f})，停车识别")
                self.set_speed(0.0, 0.0)
                # 处理识别结果的逻辑保持不变...
                if self.qrcode_result is not None and self.qrcode_result_time is not None:
                    if current_time - self.qrcode_result_time < self.qrcode_result_timeout:
                        if self.should_log("qrcode_success"):
                            self.logger.info(f"二维码识别成功，结果: {self.qrcode_result}")
                        self.arrive_qrcode += 1
                        self.stop_qrcode_recognition()
                        return
                return  # 停车识别期间不执行其他逻辑
            else:
                # 二维码距离远时，停止识别，继续正常巡线
                if self.is_qrcode_recognition_active():
                    self.stop_qrcode_recognition()
                # 不再以二维码为目标，直接进入巡线逻辑

        # 巡线逻辑
        if len(lines) > 0:
            if self.should_log("line_follow"):
                self.logger.info("执行巡线模式")
            # 计算线段中心
            centers = lines[:, :2] + lines[:, 2:]/2

            # 前 N 秒过滤左侧线，之后不过滤
            elapsed = time.time() - self.start_time
            if elapsed < self.line_filter_duration:
                if self.should_log("line_filter_active"):
                    self.logger.debug("左侧线过滤中")
                centers = centers[centers[:, 0] > 300]
            else:
                if self.should_log("line_filter_expired"):
                    self.logger.debug("左侧线过滤已过期，使用所有轨迹线")

            if len(centers) == 0:
                if self.should_log("no_lines"):
                    self.logger.warning("无有效轨迹线，停止")
                self.set_speed(0.0, 0.0)
                return

            target = np.squeeze(fitter(centers, 280))
            need_avoid = False
            
            if len(cones) > 0:
                if self.should_log("cone_detect"):
                    self.logger.info(f"检测到{len(cones)}个锥桶，检查避障")
                orig_target = target.copy()
                target = self.adjust_target_for_avoidance(target, cones)
                if not np.array_equal(orig_target, target):
                    need_avoid = True
                    if self.should_log("avoid_trigger"):
                        self.logger.info("避障触发，已调整目标点")
            angle, vel = self.calculate_control(target)
            # 动态速度调整
            if need_avoid:
                if self.avoidance_severity > 0.6:
                    vel = min(vel, 0.25)
                    if self.should_log("avoid_severe"):
                        self.logger.info(f"严重避障模式 - 速度:{vel:.2f}, 角度:{angle:.2f}")
                elif self.avoidance_severity > 0.5:
                    vel = min(vel, 0.35)
                    if self.should_log("avoid_medium"):
                        self.logger.info(f"中度避障模式 - 速度:{vel:.2f}, 角度:{angle:.2f}")
                else:
                    vel = min(vel, 0.45)
                    if self.should_log("avoid_light"):
                        self.logger.info(f"轻度避障模式 - 速度:{vel:.2f}, 角度:{angle:.2f}")
            else:
                if self.should_log("normal_follow"):
                    self.logger.info(f"正常巡线 - 速度:{vel:.2f}, 角度:{angle:.2f}")
            self.set_speed(vel, angle)
        ###########添加二维码优先级
        else:
            if self.should_log("no_line_heading"):
                self.logger.info("无轨迹线，执行航向角导航")
            self.navigate_by_heading()

    def action_task2(self, targets):
        """任务3策略：前往停车点或地标线并避障"""
        if self.arrive_park > 2:
            return
        park = np.array(targets["park"])
        cones = np.array(targets["cones"])
        lines = np.array(targets["lines"])
        if len(park) > 0:
            if self.should_log("park_detect"):
                self.logger.info("检测到停车点，前往停车点")
            park_center = np.concatenate((park[:,0:1]+park[:,2:3]/2, park[:,1:2]), axis=1)
            far_park = park_center[np.argmin(park_center[:,1])]
            target = far_park
            if len(cones) > 0:
                target = self.adjust_target_for_avoidance(target, cones)
            if target[1] > 445:  # 到达停车点距离阈值
                self.set_speed(-1.0, 0.0)
                time.sleep(0.1)
                self.stop()
                self.arrive_park += 1
                return
            angle, vel = self.calculate_control(target)
            # 避障时减速
            if self.avoidance_severity > 0.5:
                vel = min(vel, 0.3)
                if self.should_log("park_avoid"):
                    self.logger.info(f"停车点避障 - 速度:{vel:.2f}, 角度:{angle:.2f}")
            self.set_speed(vel, angle)
            self.reset_targets()
            if self.should_log("park_ctrl"):
                self.logger.info(f"停车点控制 - 速度:{vel:.2f}, 角度:{angle:.2f}")
        elif len(lines) > 0:
            if self.should_log("goto_line"):
                self.logger.info("未找到停车点，前往地标线")
            centers = lines[:, :2] + lines[:, 2:]/2
            
            # 任务3也加上左侧线过滤，使用相同的时间参数
            elapsed = time.time() - self.start_time
            if elapsed < self.line_filter_duration:
                if self.should_log("line_filter_active_task3"):
                    self.logger.debug("任务3左侧线过滤中")
                centers = centers[centers[:, 0] > 300]
            else:
                if self.should_log("line_filter_expired_task3"):
                    self.logger.debug("任务3左侧线过滤已过期，使用所有轨迹线")
            
            if len(centers) == 0:
                if self.should_log("no_lines_task3"):
                    self.logger.warning("任务3无有效轨迹线，停止")
                self.set_speed(0.0, 0.0)
                return
                
            target = np.squeeze(fitter(centers, 270))
            if len(cones) > 0:
                target = self.adjust_target_for_avoidance(target, cones)
            angle, vel = self.calculate_control(target)
            self.set_speed(0.8, angle)
            if self.should_log("line_ctrl"):
                self.logger.info(f"地标线控制 - 速度:{vel:.2f}, 角度:{angle:.2f}")
        else:
            if self.arrive_park < 2:
                if self.should_log("no_target"):
                    self.logger.info("无停车点或地标线，前进调整")
                time.sleep(0.025)
                self.set_speed(0.6, 0.0)
            else:
                if self.should_log("arrived"):
                    self.logger.info("已到达P点")
                self.stop()

    def adjust_target_for_avoidance(self, target, cones):
        """调整目标点以避开锥桶（基于距离和角度加权）"""
        cones_processed = np.concatenate(
            ((cones[:,0:1]+cones[:,2:3]/2, cones[:,1:2]+cones[:,3:4], cones[:,2:3])), axis=1)
        if self.verbose_logging:
            self.logger.info(f"原始目标: {target}, 锥桶信息: {cones_processed}")
        severity = 0.0
        if len(cones_processed) > 0:
            blocking = cones_processed[np.abs(cones_processed[:,0]-target[0]) < cones_processed[:,2]*1.2]
            if len(blocking) > 0:
                closest = blocking[np.argmax(blocking[:,1])]
                severity = closest[1]/720.0
                if closest[0] - target[0] < -closest[2]*0.3:
                    target = closest[:2] + np.array([closest[2]*1.3, 0.0])
                    if self.should_log("avoid_left"):
                        self.logger.info(f"锥桶左侧，目标右移，严重度: {severity:.2f}")
                else:
                    target = closest[:2] - np.array([closest[2]*1.3, 0.0])
                    if self.should_log("avoid_right"):
                        self.logger.info(f"锥桶右侧，目标左移，严重度: {severity:.2f}")
        self.avoidance_severity = severity
        return target

    def navigate_by_heading(self):
        """基于航向角的导航"""
        yaw_error = self.normalize_angle(self.target_yaw - self.current_yaw)
        if abs(yaw_error) <= self.yaw_tolerance:
            if self.should_log("heading_straight"):
                self.logger.info(f"航向正确({yaw_error:.2f}°)，直行")
            self.set_speed(0.3, 0.0)
        else:
            turn = min(abs(yaw_error)/2.0, 12.0)
            if yaw_error > 0:
                if self.should_log("heading_left"):
                    self.logger.info(f"需要左转({yaw_error:.2f}°)")
                self.set_speed(0.2, turn)
            else:
                if self.should_log("heading_right"):
                    self.logger.info(f"需要右转({yaw_error:.2f}°)")
                self.set_speed(0.2, -turn)

    def normalize_angle(self, angle):
        while angle > 180: angle -= 360
        while angle < -180: angle += 360
        return angle

    def stop(self):
        self.set_speed(0.0, 0.0)
        if self.should_log("stopped"):
            self.logger.info("车辆已停止")

    def calculate_control(self, target):
        if not target.any():
            return 0.0, 0.0
        if target[1] == 720:
            angle = 90 if target[0] > 580 else -90
        else:
            angle = np.degrees(np.arctan((target[0]-580) / (target[1]-720)))
        # 角度映射系数
        if abs(angle) < 5:
            scale = 4.9
        else:
            scale = 4.3
        # 默认线速度下调为0.6m/s
        vel = 0.8
        return angle/scale, vel

    def set_speed(self, x: float, z: float):
        if not hasattr(self, 'last_cmd_vel'):
            self.last_cmd_vel = {'linear': 0.0, 'angular': 0.0}
        # 限制角速度
        z = np.clip(z, -30.0, 30.0)
        # 转向平滑
        if abs(z - self.last_cmd_vel['angular']) > 10.0 and abs(self.last_cmd_vel['angular']) > 5.0:
            if self.should_log("smooth_turn"):
                self.logger.info(f"转向变化大: {self.last_cmd_vel['angular']:.2f}->{z:.2f}, 平滑处理")
            z = self.last_cmd_vel['angular'] + 0.5*(z - self.last_cmd_vel['angular'])
        # 线速度平滑
        if abs(x - self.last_cmd_vel['linear']) > 0.2:
            if self.should_log("smooth_speed"):
                self.logger.info(f"速度变化大: {self.last_cmd_vel['linear']:.2f}->{x:.2f}, 平滑")
            x = self.last_cmd_vel['linear'] + 0.5*(x - self.last_cmd_vel['linear'])
        self.last_cmd_vel['linear'] = x
        self.last_cmd_vel['angular'] = z
        if abs(z) > 5.0 or abs(x - getattr(self, 'last_linear', 0.0)) > 0.1:
            if self.should_log("cmd"):
                self.logger.info(f"控制指令 - 线速:{x:.2f}, 角速:{z:.2f}")
        self.last_linear = x
        twist = Twist()
        twist.linear.x = x
        twist.angular.z = z
        self.pub_cmd_vel.publish(twist)

    def get_bboxes(self, rois):
        roi = rois[0]
        return [roi.rect.x_offset, roi.rect.y_offset,
                roi.rect.width, roi.rect.height]

    def reset_targets(self):
        with self.lock:
            self.targets = {"qrcode": [], "park": [], "lines": [], "cones": []}


def main(args=None):
    rclpy.init(args=args)
    control_node = Control("cmj_control_yolov5_new")
    executor = MultiThreadedExecutor()
    executor.add_node(control_node)
    try:
        executor.spin()
    except KeyboardInterrupt:
        pass
    control_node.stop()
    control_node.destroy_node()
    rclpy.shutdown()

