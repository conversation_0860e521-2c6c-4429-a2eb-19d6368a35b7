// Copyright 2014 <PERSON>, LLC
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
//    * Redistributions of source code must retain the above copyright
//      notice, this list of conditions and the following disclaimer.
//
//    * Redistributions in binary form must reproduce the above copyright
//      notice, this list of conditions and the following disclaimer in the
//      documentation and/or other materials provided with the distribution.
//
//    * Neither the name of the <PERSON>, LLC nor the names of its
//      contributors may be used to endorse or promote products derived from
//      this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.

#include <memory>
#include <sstream>
#include <string>
#include <vector>

#include <chrono>

#include "hobot_usb_cam/hobot_usb_cam_node.hpp"
#include "hobot_usb_cam/utils.hpp"

const char BASE_TOPIC_NAME[] = "image_raw";

namespace usb_cam
{

  HobotUsbCamNode::HobotUsbCamNode(const rclcpp::NodeOptions &node_options)
      : Node("usb_cam", node_options),
        m_camera(new usb_cam::UsbCam()),
        m_image_msg(new sensor_msgs::msg::Image()),
        m_compressed_img_msg(nullptr),
        m_compressed_image_publisher(nullptr),
        m_image_publisher(nullptr),
        m_cam_info_publisher(nullptr),
        m_parameters(),
        m_camera_info_msg(new sensor_msgs::msg::CameraInfo()),
        m_service_capture(
            this->create_service<std_srvs::srv::SetBool>(
                "set_capture",
                std::bind(
                    &HobotUsbCamNode::service_capture,
                    this,
                    std::placeholders::_1,
                    std::placeholders::_2,
                    std::placeholders::_3)))
  {
    // declare params
    this->declare_parameter("camera_name", "default_cam");
    this->declare_parameter("camera_info_url", "");
    this->declare_parameter("framerate", 30);
    this->declare_parameter("frame_id", "default_cam");
    this->declare_parameter("image_height", 480);
    this->declare_parameter("image_width", 640);
    this->declare_parameter("io_method", "mmap");
    this->declare_parameter("pixel_format", "mjpeg");
    this->declare_parameter("av_device_format", "YUV422P");
    this->declare_parameter("video_device", "/dev/video8");
    this->declare_parameter("brightness", 50); // 0-255, -1 "leave alone"
    this->declare_parameter("contrast", -1);   // 0-255, -1 "leave alone"
    this->declare_parameter("saturation", -1); // 0-255, -1 "leave alone"
    this->declare_parameter("sharpness", -1);  // 0-255, -1 "leave alone"
    this->declare_parameter("gain", -1);       // 0-100?, -1 "leave alone"
    this->declare_parameter("auto_white_balance", true);
    this->declare_parameter("white_balance", 4000);
    this->declare_parameter("autoexposure", true);
    this->declare_parameter("exposure", 100);
    this->declare_parameter("autofocus", false);
    this->declare_parameter("focus", -1); // 0-255, -1 "leave alone"
    this->declare_parameter("zero_copy", false);
    this->declare_parameter("camera_calibration_file_path", "");

    get_params();
    init();
    m_parameters_callback_handle = add_on_set_parameters_callback(
        std::bind(
            &HobotUsbCamNode::parameters_callback, this,
            std::placeholders::_1));
  }

  HobotUsbCamNode::~HobotUsbCamNode()
  {
    RCLCPP_WARN(this->get_logger(), "Shutting down");
    m_image_msg.reset();
    m_compressed_img_msg.reset();
    m_camera_info_msg.reset();
    m_timer.reset();
    m_service_capture.reset();
    m_parameters_callback_handle.reset();

    delete (m_camera);
  }

  void HobotUsbCamNode::service_capture(
      const std::shared_ptr<rmw_request_id_t> request_header,
      const std::shared_ptr<std_srvs::srv::SetBool::Request> request,
      std::shared_ptr<std_srvs::srv::SetBool::Response> response)
  {
    (void)request_header;
    if (request->data)
    {
      m_camera->start_capturing();
      response->message = "Start Capturing";
    }
    else
    {
      m_camera->stop_capturing();
      response->message = "Stop Capturing";
    }
  }

  void HobotUsbCamNode::init()
  {
    while (m_parameters.frame_id == "")
    {
      RCLCPP_WARN_ONCE(
          this->get_logger(), "Required Parameters not set...waiting until they are set");
      get_params();
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

    // Check if given device name is an available v4l2 device
    auto available_devices = usb_cam::utils::available_devices();
    if (available_devices.find(m_parameters.device_name) == available_devices.end())
    {
      RCLCPP_ERROR_STREAM(
          this->get_logger(),
          "Device specified is not available or is not a vaild V4L2 device: `" << m_parameters.device_name << "`");
      RCLCPP_INFO(this->get_logger(), "Available V4L2 devices are:");
      for (const auto &device : available_devices)
      {
        RCLCPP_INFO_STREAM(this->get_logger(), "    " << device.first);
        RCLCPP_INFO_STREAM(this->get_logger(), "        " << device.second.card);
      }
      rclcpp::shutdown();
      return;
    }

    // if pixel format is equal to 'mjpeg', i.e. raw mjpeg stream, initialize compressed image message
    // and publisher
    if (m_parameters.pixel_format_name == "mjpeg")
    {
      m_compressed_img_msg.reset(new sensor_msgs::msg::CompressedImage());
      m_compressed_img_msg->header.frame_id = m_parameters.frame_id;
      m_compressed_image_publisher =
          this->create_publisher<sensor_msgs::msg::CompressedImage>(
              // std::string(BASE_TOPIC_NAME) + "/compressed",
              "image_compressed",
              rclcpp::QoS(1));
    }
    else
    {
      m_image_publisher = this->create_publisher<sensor_msgs::msg::Image>("image", 1);
    }
    m_cam_info_publisher =
        this->create_publisher<sensor_msgs::msg::CameraInfo>(
            "camera_info", rclcpp::QoS(100));
    m_image_msg->header.frame_id = m_parameters.frame_id;
    RCLCPP_INFO(
        this->get_logger(), "Starting '%s' (%s) at %dx%d via %s (%s) at %i FPS",
        m_parameters.camera_name.c_str(), m_parameters.device_name.c_str(),
        m_parameters.image_width, m_parameters.image_height, m_parameters.io_method_name.c_str(),
        m_parameters.pixel_format_name.c_str(), m_parameters.framerate);
    // set the IO method
    io_method_t io_method =
        usb_cam::utils::io_method_from_string(m_parameters.io_method_name);
    if (io_method == usb_cam::utils::IO_METHOD_UNKNOWN)
    {
      RCLCPP_ERROR_ONCE(
          this->get_logger(),
          "Unknown IO method '%s'", m_parameters.io_method_name.c_str());
      rclcpp::shutdown();
      return;
    }
    // load the camera info
    if (m_camera->ReadCalibrationFile(*m_camera_info_msg,
                                      m_parameters.camera_calibration_file_path))
    {
      m_camera_info_msg->header.frame_id = m_parameters.frame_id;
      m_camera_info_msg->width = m_parameters.image_width;
      m_camera_info_msg->height = m_parameters.image_height;
    }
    else
    {
      m_camera_info_msg = nullptr;
    }

    // configure the camera
    m_camera->configure(m_parameters, io_method);
    set_v4l2_params();

    // start the camera
    m_camera->start();

    // 创建hbmempub
    if (m_parameters.zero_copy)
    {
      hbmem_image_pub_1080_ =
          this->create_publisher<hbm_img_msgs::msg::HbmMsg1080P>(
              "hbmem_img", rclcpp::SensorDataQoS());
    }
    // TODO(lucasw) should this check a little faster than expected frame rate?
    // TODO(lucasw) how to do small than ms, or fractional ms- std::chrono::nanoseconds?
    const int period_ms = 1000 / m_parameters.framerate;
    m_timer = this->create_wall_timer(
        std::chrono::milliseconds(static_cast<int64_t>(period_ms)),
        std::bind(&HobotUsbCamNode::update, this));
    RCLCPP_INFO_STREAM(this->get_logger(), "Timer triggering every " << period_ms << " ms");
  }

  void HobotUsbCamNode::get_params()
  {
    auto parameters_client = std::make_shared<rclcpp::SyncParametersClient>(this);
    auto parameters = parameters_client->get_parameters(
        {"camera_name", "camera_info_url", "frame_id", "framerate", "image_height", "image_width",
         "io_method", "pixel_format", "av_device_format", "video_device", "brightness", "contrast",
         "saturation", "sharpness", "gain", "auto_white_balance", "white_balance", "autoexposure",
         "exposure", "autofocus", "focus", "zero_copy", "camera_calibration_file_path"});

    assign_params(parameters);
  }

  void HobotUsbCamNode::assign_params(const std::vector<rclcpp::Parameter> &parameters)
  {
    for (auto &parameter : parameters)
    {
      if (parameter.get_name() == "camera_name")
      {
        RCLCPP_INFO(this->get_logger(), "camera_name value: %s", parameter.value_to_string().c_str());
        m_parameters.camera_name = parameter.value_to_string();
      }
      else if (parameter.get_name() == "camera_info_url")
      {
        m_parameters.camera_info_url = parameter.value_to_string();
      }
      else if (parameter.get_name() == "frame_id")
      {
        m_parameters.frame_id = parameter.value_to_string();
      }
      else if (parameter.get_name() == "framerate")
      {
        RCLCPP_WARN(this->get_logger(), "framerate: %d", parameter.as_int());
        m_parameters.framerate = parameter.as_int();
      }
      else if (parameter.get_name() == "image_height")
      {
        m_parameters.image_height = parameter.as_int();
      }
      else if (parameter.get_name() == "image_width")
      {
        m_parameters.image_width = parameter.as_int();
      }
      else if (parameter.get_name() == "io_method")
      {
        m_parameters.io_method_name = parameter.value_to_string();
      }
      else if (parameter.get_name() == "pixel_format")
      {
        m_parameters.pixel_format_name = parameter.value_to_string();
        RCLCPP_WARN(this->get_logger(), "pixel_format_name: %s", m_parameters.pixel_format_name.c_str());
      }
      else if (parameter.get_name() == "av_device_format")
      {
        m_parameters.av_device_format = parameter.value_to_string();
      }
      else if (parameter.get_name() == "video_device")
      {
        m_parameters.device_name = parameter.value_to_string();
      }
      else if (parameter.get_name() == "brightness")
      {
        m_parameters.brightness = parameter.as_int();
      }
      else if (parameter.get_name() == "contrast")
      {
        m_parameters.contrast = parameter.as_int();
      }
      else if (parameter.get_name() == "saturation")
      {
        m_parameters.saturation = parameter.as_int();
      }
      else if (parameter.get_name() == "sharpness")
      {
        m_parameters.sharpness = parameter.as_int();
      }
      else if (parameter.get_name() == "gain")
      {
        m_parameters.gain = parameter.as_int();
      }
      else if (parameter.get_name() == "auto_white_balance")
      {
        m_parameters.auto_white_balance = parameter.as_bool();
      }
      else if (parameter.get_name() == "white_balance")
      {
        m_parameters.white_balance = parameter.as_int();
      }
      else if (parameter.get_name() == "autoexposure")
      {
        m_parameters.autoexposure = parameter.as_bool();
      }
      else if (parameter.get_name() == "exposure")
      {
        m_parameters.exposure = parameter.as_int();
      }
      else if (parameter.get_name() == "autofocus")
      {
        m_parameters.autofocus = parameter.as_bool();
      }
      else if (parameter.get_name() == "focus")
      {
        m_parameters.focus = parameter.as_int();
      }
      else if (parameter.get_name() == "zero_copy")
      {
        m_parameters.zero_copy = parameter.as_bool();
      }
      else if (parameter.get_name() == "camera_calibration_file_path")
      {
        m_parameters.camera_calibration_file_path = parameter.value_to_string();
      }
      else
      {
        RCLCPP_WARN(this->get_logger(), "Invalid parameter name: %s", parameter.get_name().c_str());
      }
    }
  }

  /// @brief Send current parameters to V4L2 device
  /// TODO(flynneva): should this actuaully be part of UsbCam class?
  void HobotUsbCamNode::set_v4l2_params()
  {
    // set camera parameters
    if (m_parameters.brightness >= 0)
    {
      RCLCPP_INFO(this->get_logger(), "Setting 'brightness' to %d", m_parameters.brightness);
      m_camera->set_v4l_parameter("brightness", m_parameters.brightness);
    }

    if (m_parameters.contrast >= 0)
    {
      RCLCPP_INFO(this->get_logger(), "Setting 'contrast' to %d", m_parameters.contrast);
      m_camera->set_v4l_parameter("contrast", m_parameters.contrast);
    }

    if (m_parameters.saturation >= 0)
    {
      RCLCPP_INFO(this->get_logger(), "Setting 'saturation' to %d", m_parameters.saturation);
      m_camera->set_v4l_parameter("saturation", m_parameters.saturation);
    }

    if (m_parameters.sharpness >= 0)
    {
      RCLCPP_INFO(this->get_logger(), "Setting 'sharpness' to %d", m_parameters.sharpness);
      m_camera->set_v4l_parameter("sharpness", m_parameters.sharpness);
    }

    if (m_parameters.gain >= 0)
    {
      RCLCPP_INFO(this->get_logger(), "Setting 'gain' to %d", m_parameters.gain);
      m_camera->set_v4l_parameter("gain", m_parameters.gain);
    }

    // check auto white balance
    if (m_parameters.auto_white_balance)
    {
      m_camera->set_v4l_parameter("white_balance_temperature_auto", 1);
      RCLCPP_INFO(this->get_logger(), "Setting 'white_balance_temperature_auto' to %d", 1);
    }
    else
    {
      RCLCPP_INFO(this->get_logger(), "Setting 'white_balance' to %d", m_parameters.white_balance);
      m_camera->set_v4l_parameter("white_balance_temperature_auto", 0);
      m_camera->set_v4l_parameter("white_balance_temperature", m_parameters.white_balance);
    }

    // check auto exposure
    if (!m_parameters.autoexposure)
    {
      RCLCPP_INFO(this->get_logger(), "Setting 'exposure_auto' to %d", 1);
      RCLCPP_INFO(this->get_logger(), "Setting 'exposure' to %d", m_parameters.exposure);
      // turn down exposure control (from max of 3)
      m_camera->set_v4l_parameter("exposure_auto", 1);
      // change the exposure level
      m_camera->set_v4l_parameter("exposure_absolute", m_parameters.exposure);
    }
    else
    {
      RCLCPP_INFO(this->get_logger(), "Setting 'exposure_auto' to %d", 3);
      m_camera->set_v4l_parameter("exposure_auto", 3);
    }

    // check auto focus
    if (m_parameters.autofocus)
    {
      m_camera->set_auto_focus(1);
      RCLCPP_INFO(this->get_logger(), "Setting 'focus_auto' to %d", 1);
      m_camera->set_v4l_parameter("focus_auto", 1);
    }
    else
    {
      RCLCPP_INFO(this->get_logger(), "Setting 'focus_auto' to %d", 0);
      m_camera->set_v4l_parameter("focus_auto", 0);
      if (m_parameters.focus >= 0)
      {
        RCLCPP_INFO(this->get_logger(), "Setting 'focus_absolute' to %d", m_parameters.focus);
        m_camera->set_v4l_parameter("focus_absolute", m_parameters.focus);
      }
    }
  }

  bool HobotUsbCamNode::take_and_send_image()
  {
    // Only resize if required
    if (sizeof(m_image_msg->data) != m_camera->get_image_size_in_bytes())
    {
      m_image_msg->width = m_camera->get_image_width();
      m_image_msg->height = m_camera->get_image_height();
      m_image_msg->encoding = m_camera->get_pixel_format()->ros();
      m_image_msg->step = m_camera->get_image_step();
      if (m_image_msg->step == 0)
      {
        // Some formats don't have a linesize specified by v4l2
        // Fall back to manually calculating it step = size / height
        m_image_msg->step = m_camera->get_image_size_in_bytes() / m_image_msg->height;
      }
      m_image_msg->data.resize(m_camera->get_image_size_in_bytes());
    }
    // grab the image, pass image msg buffer to fill
    m_camera->get_image(reinterpret_cast<char *>(&m_image_msg->data[0]));
    RCLCPP_INFO(this->get_logger(), "take_and_send_image,size:%d", m_camera->get_image_data_size());
    auto stamp = m_camera->get_image_timestamp();
    m_image_msg->header.stamp.sec = stamp.tv_sec;
    m_image_msg->header.stamp.nanosec = stamp.tv_nsec;

    RCLCPP_INFO(this->get_logger(), "push image data");
    m_image_publisher->publish(*m_image_msg);
    if (m_camera_info_msg != nullptr)
    {
      m_camera_info_msg->header = m_image_msg->header;
      m_cam_info_publisher->publish(*m_camera_info_msg);
    }
    return true;
  }

  bool HobotUsbCamNode::hbmem_take_and_send_image()
  {
    // static int mSendIdx = 0;
    auto loanedMsg = hbmem_image_pub_1080_->borrow_loaned_message();
    if (loanedMsg.is_valid())
    {
      auto &msg = loanedMsg.get();
      msg.width = m_camera->get_image_width();
      msg.height = m_camera->get_image_height();
      auto encoding = m_camera->get_pixel_format()->ros();
      memcpy(&msg.encoding[0], encoding.c_str(), encoding.length());
      msg.step = m_camera->get_image_step();
      m_camera->get_image(reinterpret_cast<char *>(&msg.data[0]));
      msg.data_size = m_camera->get_image_data_size();
      auto stamp = m_camera->get_image_timestamp();
      msg.time_stamp.sec = stamp.tv_sec;
      msg.time_stamp.nanosec = stamp.tv_nsec;
      msg.index = mSendIdx++;
      RCLCPP_INFO(this->get_logger(), "push image data by hbmem");
      hbmem_image_pub_1080_->publish(std::move(loanedMsg));
      if (m_camera_info_msg != nullptr)
      {
        m_camera_info_msg->header.stamp = msg.time_stamp;
        m_camera_info_msg->header = m_image_msg->header;
        m_cam_info_publisher->publish(*m_camera_info_msg);
      }
    }
    else
    {
      RCLCPP_WARN(rclcpp::get_logger("usb_cam_node"),
                  "borrow_loaned_message failed");
    }
    return true;
  }

  bool HobotUsbCamNode::take_and_send_image_mjpeg()
  {
    m_compressed_img_msg->format = "jpeg";
    // grab the image, pass image msg buffer to fill
    auto data_buf = m_camera->get_image();
    m_compressed_img_msg->data.resize(m_camera->get_image_data_size());
    memcpy(&m_compressed_img_msg->data[0], data_buf, m_camera->get_image_data_size());

    auto stamp = m_camera->get_image_timestamp();
    m_compressed_img_msg->header.stamp.sec = stamp.tv_sec;
    m_compressed_img_msg->header.stamp.nanosec = stamp.tv_nsec;

    m_compressed_image_publisher->publish(*m_compressed_img_msg);
    // 发布图像时间
    // auto now = std::chrono::steady_clock::now();
    // RCLCPP_WARN(this->get_logger(), "发布图像时间:%d", std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count());
    m_compressed_img_msg->data.resize(m_camera->get_image_size_in_bytes());
    if (m_camera_info_msg != nullptr)
    {
      m_camera_info_msg->header = m_compressed_img_msg->header;
      m_cam_info_publisher->publish(*m_camera_info_msg);
    }
    RCLCPP_INFO(this->get_logger(), "take_and_send_image_mjpeg");
    return true;
  }

  rcl_interfaces::msg::SetParametersResult HobotUsbCamNode::parameters_callback(
      const std::vector<rclcpp::Parameter> &parameters)
  {
    RCLCPP_DEBUG(this->get_logger(), "Setting parameters for %s", m_parameters.camera_name.c_str());
    m_timer->reset();
    assign_params(parameters);
    set_v4l2_params();
    rcl_interfaces::msg::SetParametersResult result;
    result.successful = true;
    result.reason = "success";
    return result;
  
  }

  void HobotUsbCamNode::update()
  {
    if (m_camera->is_capturing())
    {
      // If the camera exposure longer higher than the framerate period
      // then that caps the framerate.
      // auto t0 = now();
      bool isSuccessful = m_parameters.zero_copy ? hbmem_take_and_send_image() : ((m_parameters.pixel_format_name == "mjpeg") ? take_and_send_image_mjpeg() : take_and_send_image());

      if (!isSuccessful)
      {
        RCLCPP_WARN_ONCE(this->get_logger(), "USB camera did not respond in time.");
      }
    }
  }
} // namespace usb_cam

// #include "rclcpp_components/register_node_macro.hpp"
// RCLCPP_COMPONENTS_REGISTER_NODE(usb_cam::HobotUsbCamNode)
