import os
from glob import glob
from setuptools import setup

package_name = "ahpu_qrcode"

setup(
    name=package_name,
    version="0.0.0",
    packages=[package_name],
    data_files=[
        ("share/ament_index/resource_index/packages", ["resource/" + package_name]),
        ("share/" + package_name, ["package.xml"]),
        (
            os.path.join("share", package_name, "launch"),
            glob(os.path.join("launch", "*.launch.py")),
        ),
    ],
    install_requires=["setuptools"],
    zip_safe=True,
    maintainer="root",
    maintainer_email="<EMAIL>",
    description="TODO: Package description",
    license="TODO: License declaration",
    tests_require=["pytest"],
    entry_points={
        "console_scripts": [
            "qrcode_pub=ahpu_qrcode.qrcode_pub:main",
            "qrcode_resnet=ahpu_qrcode.qrcode_resnet:main",
            "qrcode_pub_2025=ahpu_qrcode.qrcode_pub_2025:main",
        ],
    },
)
